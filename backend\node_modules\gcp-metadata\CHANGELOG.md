# Changelog

[npm history][1]

[1]: https://www.npmjs.com/package/gcp-metadata?activeTab=versions

## [5.3.0](https://github.com/googleapis/gcp-metadata/compare/v5.2.0...v5.3.0) (2023-06-28)


### Features

* Metadata Server Detection Configuration ([#562](https://github.com/googleapis/gcp-metadata/issues/562)) ([8c7c715](https://github.com/googleapis/gcp-metadata/commit/8c7c715f1fc22ad65554a745a93915713ca6698f))

## [5.2.0](https://github.com/googleapis/gcp-metadata/compare/v5.1.0...v5.2.0) (2023-01-03)


### Features

* Export `gcp-residency` tools ([#552](https://github.com/googleapis/gcp-metadata/issues/552)) ([ba9ae24](https://github.com/googleapis/gcp-metadata/commit/ba9ae24331b53199f81e97b6a88414050cfcf546))

## [5.1.0](https://github.com/googleapis/gcp-metadata/compare/v5.0.1...v5.1.0) (2022-12-07)


### Features

* Extend GCP Residency Detection Support ([#528](https://github.com/googleapis/gcp-metadata/issues/528)) ([2b35bb0](https://github.com/googleapis/gcp-metadata/commit/2b35bb0e6fb1a18294aeeebba91a6bf7b400385a))

## [5.0.1](https://github.com/googleapis/gcp-metadata/compare/v5.0.0...v5.0.1) (2022-09-09)


### Bug Fixes

* Remove pip install statements ([#1546](https://github.com/googleapis/gcp-metadata/issues/1546)) ([#529](https://github.com/googleapis/gcp-metadata/issues/529)) ([064c64c](https://github.com/googleapis/gcp-metadata/commit/064c64cec160ffe645e6946a5125960e3e269d7f))

## [5.0.0](https://github.com/googleapis/gcp-metadata/compare/v4.3.1...v5.0.0) (2022-04-22)


### ⚠ BREAKING CHANGES

* drop node 10, update typescript to 4.6.3 (#519)

### Build System

* drop node 10, update typescript to 4.6.3 ([#519](https://github.com/googleapis/gcp-metadata/issues/519)) ([688749b](https://github.com/googleapis/gcp-metadata/commit/688749bc50407f3cd127a0b10ae09487d6fe5aea))

### [4.3.1](https://www.github.com/googleapis/gcp-metadata/compare/v4.3.0...v4.3.1) (2021-09-02)


### Bug Fixes

* **build:** switch primary branch to main ([#481](https://www.github.com/googleapis/gcp-metadata/issues/481)) ([8a7965c](https://www.github.com/googleapis/gcp-metadata/commit/8a7965c47c077ef766e4b416358630c0b24b0af2))

## [4.3.0](https://www.github.com/googleapis/gcp-metadata/compare/v4.2.1...v4.3.0) (2021-06-10)


### Features

* add `gcf-owl-bot[bot]` to `ignoreAuthors` ([#450](https://www.github.com/googleapis/gcp-metadata/issues/450)) ([6a0f9ad](https://www.github.com/googleapis/gcp-metadata/commit/6a0f9ad09b6d16370d08c5d60541ce3ef64a9f97))

### [4.2.1](https://www.github.com/googleapis/gcp-metadata/compare/v4.2.0...v4.2.1) (2020-10-29)


### Bug Fixes

* **deps:** update dependency gaxios to v4 ([#420](https://www.github.com/googleapis/gcp-metadata/issues/420)) ([b99fb07](https://www.github.com/googleapis/gcp-metadata/commit/b99fb0764b8dbb8b083f73b8007816914db4f09a))

## [4.2.0](https://www.github.com/googleapis/gcp-metadata/compare/v4.1.4...v4.2.0) (2020-09-15)


### Features

* add support for GCE_METADATA_HOST environment variable ([#406](https://www.github.com/googleapis/gcp-metadata/issues/406)) ([eaf128a](https://www.github.com/googleapis/gcp-metadata/commit/eaf128ad5afc4357cde72d19b017b9474c070fea))

### [4.1.4](https://www.github.com/googleapis/gcp-metadata/compare/v4.1.3...v4.1.4) (2020-07-15)


### Bug Fixes

* **deps:** update dependency json-bigint to v1 ([#382](https://www.github.com/googleapis/gcp-metadata/issues/382)) ([ab4d8c3](https://www.github.com/googleapis/gcp-metadata/commit/ab4d8c3022903206d433bafc47c27815c6f85e36))

### [4.1.3](https://www.github.com/googleapis/gcp-metadata/compare/v4.1.2...v4.1.3) (2020-07-13)


### Bug Fixes

* **deps:** update dependency json-bigint to ^0.4.0 ([#378](https://www.github.com/googleapis/gcp-metadata/issues/378)) ([b214280](https://www.github.com/googleapis/gcp-metadata/commit/b2142807928c8c032509277900d35fccd1023f0f))

### [4.1.2](https://www.github.com/googleapis/gcp-metadata/compare/v4.1.1...v4.1.2) (2020-07-10)


### Bug Fixes

* **deps:** roll back dependency gcp-metadata to ^4.1.0 ([#373](https://www.github.com/googleapis/gcp-metadata/issues/373)) ([a45adef](https://www.github.com/googleapis/gcp-metadata/commit/a45adefd92418faa08c8a5014cedb844d1eb3ae6))

### [4.1.1](https://www.github.com/googleapis/gcp-metadata/compare/v4.1.0...v4.1.1) (2020-07-09)


### Bug Fixes

* typeo in nodejs .gitattribute ([#371](https://www.github.com/googleapis/gcp-metadata/issues/371)) ([5b4bb1c](https://www.github.com/googleapis/gcp-metadata/commit/5b4bb1c85e67e3ef0a6d1ec2ea316d560e03092f))

## [4.1.0](https://www.github.com/googleapis/gcp-metadata/compare/v4.0.1...v4.1.0) (2020-05-05)


### Features

* Introduces the GCE_METADATA_IP to allow using a different IP address for the GCE metadata server. ([#346](https://www.github.com/googleapis/gcp-metadata/issues/346)) ([ec0f82d](https://www.github.com/googleapis/gcp-metadata/commit/ec0f82d022b4b3aac95e94ee1d8e53cfac3b14a4))


### Bug Fixes

* do not check secondary host if GCE_METADATA_IP set ([#352](https://www.github.com/googleapis/gcp-metadata/issues/352)) ([64fa7d6](https://www.github.com/googleapis/gcp-metadata/commit/64fa7d68cbb76f455a3bfdcb27d58e7775eb789a))
* warn rather than throwing when we fail to connect to metadata server ([#351](https://www.github.com/googleapis/gcp-metadata/issues/351)) ([754a6c0](https://www.github.com/googleapis/gcp-metadata/commit/754a6c07d1a72615cbb5ebf9ee04475a9a12f1c0))

### [4.0.1](https://www.github.com/googleapis/gcp-metadata/compare/v4.0.0...v4.0.1) (2020-04-14)


### Bug Fixes

* **deps:** update dependency gaxios to v3 ([#326](https://www.github.com/googleapis/gcp-metadata/issues/326)) ([5667178](https://www.github.com/googleapis/gcp-metadata/commit/5667178429baff71ad5dab2a96f97f27b2106d57))
* apache license URL ([#468](https://www.github.com/googleapis/gcp-metadata/issues/468)) ([#336](https://www.github.com/googleapis/gcp-metadata/issues/336)) ([195dcd2](https://www.github.com/googleapis/gcp-metadata/commit/195dcd2d227ba496949e7ec0dcd77e5b9269066c))

## [4.0.0](https://www.github.com/googleapis/gcp-metadata/compare/v3.5.0...v4.0.0) (2020-03-19)


### ⚠ BREAKING CHANGES

* typescript@3.7.x has breaking changes; compiler now targets es2015
* drops Node 8 from engines field (#315)

### Features

* drops Node 8 from engines field ([#315](https://www.github.com/googleapis/gcp-metadata/issues/315)) ([acb6233](https://www.github.com/googleapis/gcp-metadata/commit/acb62337e8ba7f0b259ae4e553f19c5786207d84))


### Build System

* switch to latest typescirpt/gts ([#317](https://www.github.com/googleapis/gcp-metadata/issues/317)) ([fbb7158](https://www.github.com/googleapis/gcp-metadata/commit/fbb7158be62c9f1949b69079e35113be1e10495c))

## [3.5.0](https://www.github.com/googleapis/gcp-metadata/compare/v3.4.0...v3.5.0) (2020-03-03)


### Features

* add ECONNREFUSED to list of known errors for isAvailable() ([#309](https://www.github.com/googleapis/gcp-metadata/issues/309)) ([17ff6ea](https://www.github.com/googleapis/gcp-metadata/commit/17ff6ea361d02de31463532d4ab4040bf6276e0b))

## [3.4.0](https://www.github.com/googleapis/gcp-metadata/compare/v3.3.1...v3.4.0) (2020-02-24)


### Features

* significantly increase timeout if GCF environment detected ([#300](https://www.github.com/googleapis/gcp-metadata/issues/300)) ([8e507c6](https://www.github.com/googleapis/gcp-metadata/commit/8e507c645f69a11f508884b3181dc4414e579fcc))

### [3.3.1](https://www.github.com/googleapis/gcp-metadata/compare/v3.3.0...v3.3.1) (2020-01-30)


### Bug Fixes

* **isAvailable:** handle EHOSTDOWN and EHOSTUNREACH error codes ([#291](https://www.github.com/googleapis/gcp-metadata/issues/291)) ([ba8d9f5](https://www.github.com/googleapis/gcp-metadata/commit/ba8d9f50eac6cf8b439c1b66c48ace146c75f6e2))

## [3.3.0](https://www.github.com/googleapis/gcp-metadata/compare/v3.2.3...v3.3.0) (2019-12-16)


### Features

* add environment variable for configuring environment detection ([#275](https://www.github.com/googleapis/gcp-metadata/issues/275)) ([580cfa4](https://www.github.com/googleapis/gcp-metadata/commit/580cfa4a5f5d0041aa09ae85cfc5a4575dd3957f))
* cache response from isAvailable() method ([#274](https://www.github.com/googleapis/gcp-metadata/issues/274)) ([a05e13f](https://www.github.com/googleapis/gcp-metadata/commit/a05e13f1d1d61b1f9b9b1703bc37cdbdc022c93b))


### Bug Fixes

* fastFailMetadataRequest should not reject, if response already happened ([#273](https://www.github.com/googleapis/gcp-metadata/issues/273)) ([a6590c4](https://www.github.com/googleapis/gcp-metadata/commit/a6590c4fd8bc2dff3995c83d4c9175d5bd9f5e4a))

### [3.2.3](https://www.github.com/googleapis/gcp-metadata/compare/v3.2.2...v3.2.3) (2019-12-12)


### Bug Fixes

* **deps:** pin TypeScript below 3.7.0 ([e4bf622](https://www.github.com/googleapis/gcp-metadata/commit/e4bf622e6654a51ddffc0921a15250130591db2f))

### [3.2.2](https://www.github.com/googleapis/gcp-metadata/compare/v3.2.1...v3.2.2) (2019-11-13)


### Bug Fixes

* **docs:** add jsdoc-region-tag plugin ([#264](https://www.github.com/googleapis/gcp-metadata/issues/264)) ([af8362b](https://www.github.com/googleapis/gcp-metadata/commit/af8362b5a35d270af00cb3696bbf7344810e9b0c))

### [3.2.1](https://www.github.com/googleapis/gcp-metadata/compare/v3.2.0...v3.2.1) (2019-11-08)


### Bug Fixes

* **deps:** update gaxios ([#257](https://www.github.com/googleapis/gcp-metadata/issues/257)) ([ba6e0b6](https://www.github.com/googleapis/gcp-metadata/commit/ba6e0b668635b4aa4ed10535ff021c02b2edf5ea))

## [3.2.0](https://www.github.com/googleapis/gcp-metadata/compare/v3.1.0...v3.2.0) (2019-10-10)


### Features

* add DEBUG_AUTH for digging into authentication issues ([#254](https://www.github.com/googleapis/gcp-metadata/issues/254)) ([804156d](https://www.github.com/googleapis/gcp-metadata/commit/804156d))

## [3.1.0](https://www.github.com/googleapis/gcp-metadata/compare/v3.0.0...v3.1.0) (2019-10-07)


### Features

* don't throw on ENETUNREACH ([#250](https://www.github.com/googleapis/gcp-metadata/issues/250)) ([88f2101](https://www.github.com/googleapis/gcp-metadata/commit/88f2101))

## [3.0.0](https://www.github.com/googleapis/gcp-metadata/compare/v2.0.4...v3.0.0) (2019-09-17)


### ⚠ BREAKING CHANGES

* isAvailable now tries both DNS and IP, choosing whichever responds first (#239)

### Features

* isAvailable now tries both DNS and IP, choosing whichever responds first ([#239](https://www.github.com/googleapis/gcp-metadata/issues/239)) ([25bc116](https://www.github.com/googleapis/gcp-metadata/commit/25bc116))

### [2.0.4](https://www.github.com/googleapis/gcp-metadata/compare/v2.0.3...v2.0.4) (2019-09-13)


### Bug Fixes

* IP address takes 15 seconds to timeout, vs., metadata returning immediately ([#235](https://www.github.com/googleapis/gcp-metadata/issues/235)) ([d04207b](https://www.github.com/googleapis/gcp-metadata/commit/d04207b))
* use 3s timeout rather than 15 default ([#237](https://www.github.com/googleapis/gcp-metadata/issues/237)) ([231ca5c](https://www.github.com/googleapis/gcp-metadata/commit/231ca5c))

### [2.0.3](https://www.github.com/googleapis/gcp-metadata/compare/v2.0.2...v2.0.3) (2019-09-12)


### Bug Fixes

* use IP for metadata server ([#233](https://www.github.com/googleapis/gcp-metadata/issues/233)) ([20a15cb](https://www.github.com/googleapis/gcp-metadata/commit/20a15cb))

### [2.0.2](https://www.github.com/googleapis/gcp-metadata/compare/v2.0.1...v2.0.2) (2019-08-26)


### Bug Fixes

* allow calls with no request, add JSON proto ([#224](https://www.github.com/googleapis/gcp-metadata/issues/224)) ([dc758b1](https://www.github.com/googleapis/gcp-metadata/commit/dc758b1))

### [2.0.1](https://www.github.com/googleapis/gcp-metadata/compare/v2.0.0...v2.0.1) (2019-06-26)


### Bug Fixes

* **docs:** make anchors work in jsdoc ([#212](https://www.github.com/googleapis/gcp-metadata/issues/212)) ([9174b43](https://www.github.com/googleapis/gcp-metadata/commit/9174b43))

## [2.0.0](https://www.github.com/googleapis/gcp-metadata/compare/v1.0.0...v2.0.0) (2019-05-07)


### Bug Fixes

* **deps:** update dependency gaxios to v2 ([#191](https://www.github.com/googleapis/gcp-metadata/issues/191)) ([ac8c1ef](https://www.github.com/googleapis/gcp-metadata/commit/ac8c1ef))


### Build System

* upgrade engines field to >=8.10.0 ([#194](https://www.github.com/googleapis/gcp-metadata/issues/194)) ([97c23c8](https://www.github.com/googleapis/gcp-metadata/commit/97c23c8))


### BREAKING CHANGES

* upgrade engines field to >=8.10.0 (#194)

## v1.0.0

02-14-2019 16:00 PST

### Bug Fixes
- fix: ask gaxios for text and not json ([#152](https://github.com/googleapis/gcp-metadata/pull/152))

### Documentation
- docs: update links in contrib guide ([#168](https://github.com/googleapis/gcp-metadata/pull/168))
- docs: add lint/fix example to contributing guide ([#160](https://github.com/googleapis/gcp-metadata/pull/160))

### Internal / Testing Changes
- build: use linkinator for docs test ([#166](https://github.com/googleapis/gcp-metadata/pull/166))
- chore(deps): update dependency @types/tmp to v0.0.34 ([#167](https://github.com/googleapis/gcp-metadata/pull/167))
- build: create docs test npm scripts ([#165](https://github.com/googleapis/gcp-metadata/pull/165))
- test: run system tests on GCB ([#157](https://github.com/googleapis/gcp-metadata/pull/157))
- build: test using @grpc/grpc-js in CI ([#164](https://github.com/googleapis/gcp-metadata/pull/164))
- chore: move CONTRIBUTING.md to root ([#162](https://github.com/googleapis/gcp-metadata/pull/162))
- chore(deps): update dependency gcx to v0.1.1 ([#159](https://github.com/googleapis/gcp-metadata/pull/159))
- chore(deps): update dependency gcx to v0.1.0 ([#158](https://github.com/googleapis/gcp-metadata/pull/158))
- chore(deps): update dependency gcx to v0.0.4 ([#155](https://github.com/googleapis/gcp-metadata/pull/155))
- chore(deps): update dependency googleapis to v37 ([#156](https://github.com/googleapis/gcp-metadata/pull/156))
- build: ignore googleapis.com in doc link check ([#153](https://github.com/googleapis/gcp-metadata/pull/153))
- build: check broken links in generated docs ([#149](https://github.com/googleapis/gcp-metadata/pull/149))
- chore(build): inject yoshi automation key ([#148](https://github.com/googleapis/gcp-metadata/pull/148))

## v0.9.3

12-10-2018 16:16 PST

### Dependencies
- chore(deps): update dependency googleapis to v36 ([#135](https://github.com/googleapis/gcp-metadata/pull/135))
- chore(deps): use gaxios for http requests ([#121](https://github.com/googleapis/gcp-metadata/pull/121))
- chore(deps): update dependency gts to ^0.9.0 ([#123](https://github.com/googleapis/gcp-metadata/pull/123))

### Internal / Testing Changes
- fix(build): fix Kokoro release script ([#141](https://github.com/googleapis/gcp-metadata/pull/141))
- Release v0.9.2 ([#140](https://github.com/googleapis/gcp-metadata/pull/140))
- build: add Kokoro configs for autorelease ([#138](https://github.com/googleapis/gcp-metadata/pull/138))
- Release gcp-metadata v0.9.1 ([#139](https://github.com/googleapis/gcp-metadata/pull/139))
- chore: always nyc report before calling codecov ([#134](https://github.com/googleapis/gcp-metadata/pull/134))
- chore: nyc ignore build/test by default ([#133](https://github.com/googleapis/gcp-metadata/pull/133))
- Sync repo build files ([#131](https://github.com/googleapis/gcp-metadata/pull/131))
- fix(build): fix system key decryption ([#128](https://github.com/googleapis/gcp-metadata/pull/128))
- refactor: use execa, move post install test to system ([#127](https://github.com/googleapis/gcp-metadata/pull/127))
- chore: add a synth.metadata
- test: add a system test ([#126](https://github.com/googleapis/gcp-metadata/pull/126))
- chore: update eslintignore config ([#122](https://github.com/googleapis/gcp-metadata/pull/122))
- chore: use latest npm on Windows ([#120](https://github.com/googleapis/gcp-metadata/pull/120))
- chore: update CircleCI config ([#119](https://github.com/googleapis/gcp-metadata/pull/119))
- chore: include build in eslintignore ([#115](https://github.com/googleapis/gcp-metadata/pull/115))

## v0.9.2

12-10-2018 14:01 PST

- chore(deps): update dependency googleapis to v36 ([#135](https://github.com/googleapis/gcp-metadata/pull/135))
- chore: always nyc report before calling codecov ([#134](https://github.com/googleapis/gcp-metadata/pull/134))
- chore: nyc ignore build/test by default ([#133](https://github.com/googleapis/gcp-metadata/pull/133))
- chore: Re-generated  to pick up changes in the API or client library generator. ([#131](https://github.com/googleapis/gcp-metadata/pull/131))
- fix(build): fix system key decryption ([#128](https://github.com/googleapis/gcp-metadata/pull/128))
- chore(deps): use gaxios for http requests ([#121](https://github.com/googleapis/gcp-metadata/pull/121))
- refactor: use execa, move post install test to system ([#127](https://github.com/googleapis/gcp-metadata/pull/127))
- chore: add a synth.metadata
- test: add a system test ([#126](https://github.com/googleapis/gcp-metadata/pull/126))
- chore(deps): update dependency gts to ^0.9.0 ([#123](https://github.com/googleapis/gcp-metadata/pull/123))
- chore: update eslintignore config ([#122](https://github.com/googleapis/gcp-metadata/pull/122))
- chore: use latest npm on Windows ([#120](https://github.com/googleapis/gcp-metadata/pull/120))
- chore: update CircleCI config ([#119](https://github.com/googleapis/gcp-metadata/pull/119))
- chore: include build in eslintignore ([#115](https://github.com/googleapis/gcp-metadata/pull/115))
- build: add Kokoro configs for autorelease ([#138](https://github.com/googleapis/gcp-metadata/pull/138))

## v0.9.1

12-10-2018 11:53 PST

- chore(deps): update dependency googleapis to v36 ([#135](https://github.com/googleapis/gcp-metadata/pull/135))
- chore: always nyc report before calling codecov ([#134](https://github.com/googleapis/gcp-metadata/pull/134))
- chore: nyc ignore build/test by default ([#133](https://github.com/googleapis/gcp-metadata/pull/133))
- chore: Re-generated  to pick up changes in the API or client library generator. ([#131](https://github.com/googleapis/gcp-metadata/pull/131))
- fix(build): fix system key decryption ([#128](https://github.com/googleapis/gcp-metadata/pull/128))
- chore(deps): use gaxios for http requests ([#121](https://github.com/googleapis/gcp-metadata/pull/121))
- refactor: use execa, move post install test to system ([#127](https://github.com/googleapis/gcp-metadata/pull/127))
- chore: add a synth.metadata
- test: add a system test ([#126](https://github.com/googleapis/gcp-metadata/pull/126))
- chore(deps): update dependency gts to ^0.9.0 ([#123](https://github.com/googleapis/gcp-metadata/pull/123))
- chore: update eslintignore config ([#122](https://github.com/googleapis/gcp-metadata/pull/122))
- chore: use latest npm on Windows ([#120](https://github.com/googleapis/gcp-metadata/pull/120))
- chore: update CircleCI config ([#119](https://github.com/googleapis/gcp-metadata/pull/119))
- chore: include build in eslintignore ([#115](https://github.com/googleapis/gcp-metadata/pull/115))

## v0.9.0

10-26-2018 13:10 PDT

- feat: allow custom headers ([#109](https://github.com/googleapis/gcp-metadata/pull/109))
- chore: update issue templates ([#108](https://github.com/googleapis/gcp-metadata/pull/108))
- chore: remove old issue template ([#106](https://github.com/googleapis/gcp-metadata/pull/106))
- build: run tests on node11 ([#105](https://github.com/googleapis/gcp-metadata/pull/105))
- chores(build): do not collect sponge.xml from windows builds ([#104](https://github.com/googleapis/gcp-metadata/pull/104))
- chores(build): run codecov on continuous builds ([#102](https://github.com/googleapis/gcp-metadata/pull/102))
- chore(deps): update dependency nock to v10 ([#103](https://github.com/googleapis/gcp-metadata/pull/103))
- chore: update new issue template ([#101](https://github.com/googleapis/gcp-metadata/pull/101))
- build: fix codecov uploading on Kokoro ([#97](https://github.com/googleapis/gcp-metadata/pull/97))
- Update kokoro config ([#95](https://github.com/googleapis/gcp-metadata/pull/95))
- Update CI config ([#93](https://github.com/googleapis/gcp-metadata/pull/93))
- Update kokoro config ([#91](https://github.com/googleapis/gcp-metadata/pull/91))
- Re-generate library using /synth.py ([#90](https://github.com/googleapis/gcp-metadata/pull/90))
- test: remove appveyor config ([#89](https://github.com/googleapis/gcp-metadata/pull/89))
- Update kokoro config ([#88](https://github.com/googleapis/gcp-metadata/pull/88))
- Enable prefer-const in the eslint config ([#87](https://github.com/googleapis/gcp-metadata/pull/87))
- Enable no-var in eslint ([#86](https://github.com/googleapis/gcp-metadata/pull/86))

### New Features

A new option, `headers`, has been added to allow metadata queries to be sent with custom headers.

## v0.8.0

**This release has breaking changes**.  Please take care when upgrading to the latest version.

#### Dropped support for Node.js 4.x and 9.x
This library is no longer tested against versions 4.x and 9.x of Node.js.  Please upgrade to the latest supported LTS version!

#### Return type of `instance()` and `project()` has changed
The `instance()` and `project()` methods are much more selective about which properties they will accept.

The only accepted properties are `params` and `properties`.  The `instance()` and `project()` methods also now directly return the data instead of a response object.

#### Changes in how large number valued properties are handled

Previously large number-valued properties were being silently losing precision when
returned by this library (as a number). In the cases where a number valued property
returned by the metadata service is too large to represent as a JavaScript number, we
will now return the value as a BigNumber (from the bignumber.js) library. Numbers that
do fit into the JavaScript number range will continue to be returned as numbers.
For more details see [#74](https://github.com/googleapis/gcp-metadata/pull/74).

### Breaking Changes
- chore: drop support for node.js 4 and 9 ([#68](https://github.com/googleapis/gcp-metadata/pull/68))
- fix: quarantine axios config ([#62](https://github.com/googleapis/gcp-metadata/pull/62))

### Implementation Changes
- fix: properly handle large numbers in responses ([#74](https://github.com/googleapis/gcp-metadata/pull/74))

### Dependencies
- chore(deps): update dependency pify to v4 ([#73](https://github.com/googleapis/gcp-metadata/pull/73))

### Internal / Testing Changes
- Move to the new github org ([#84](https://github.com/googleapis/gcp-metadata/pull/84))
- Update CI config ([#83](https://github.com/googleapis/gcp-metadata/pull/83))
- Retry npm install in CI ([#81](https://github.com/googleapis/gcp-metadata/pull/81))
- Update CI config ([#79](https://github.com/googleapis/gcp-metadata/pull/79))
- chore(deps): update dependency nyc to v13 ([#77](https://github.com/googleapis/gcp-metadata/pull/77))
- add key for system tests
- increase kitchen test timeout
- add a lint npm script
- update npm scripts
- add a synth file and run it ([#75](https://github.com/googleapis/gcp-metadata/pull/75))
- chore(deps): update dependency assert-rejects to v1 ([#72](https://github.com/googleapis/gcp-metadata/pull/72))
- chore: ignore package-log.json ([#71](https://github.com/googleapis/gcp-metadata/pull/71))
- chore: update renovate config ([#70](https://github.com/googleapis/gcp-metadata/pull/70))
- test: throw on deprecation
- chore(deps): update dependency typescript to v3 ([#67](https://github.com/googleapis/gcp-metadata/pull/67))
- chore: make it OSPO compliant ([#66](https://github.com/googleapis/gcp-metadata/pull/66))
- chore(deps): update dependency gts to ^0.8.0 ([#65](https://github.com/googleapis/gcp-metadata/pull/65))
