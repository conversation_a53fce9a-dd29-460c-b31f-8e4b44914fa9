# Logistics Platform Backend

A production-ready, scalable backend for a comprehensive logistics and intra-city transportation booking platform built with the MERN stack.

## Features

- **Multi-role Authentication**: User, Driver, Admin with JWT-based authentication
- **Real-time Tracking**: WebSocket integration for live location updates
- **Payment Processing**: Stripe integration with multiple payment methods
- **Notification System**: Multi-channel notifications (SMS, Email, Push)
- **Admin Dashboard**: Comprehensive management and analytics APIs
- **File Upload**: Document management for driver onboarding
- **Rate Limiting**: Protection against abuse and DDoS attacks
- **Caching**: Redis integration for improved performance
- **Logging**: Structured logging with Winston
- **Testing**: Comprehensive test suite with Jest

## Tech Stack

- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose ODM
- **Cache**: Redis
- **Authentication**: JWT with refresh tokens
- **Real-time**: Socket.IO
- **File Upload**: Multer
- **Validation**: Joi
- **Testing**: Jest, Supertest
- **Logging**: Winston
- **Security**: Helmet, CORS, Rate limiting

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- MongoDB (v4.4 or higher)
- Redis (v6 or higher)
- npm or yarn

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd backend
```

2. Install dependencies
```bash
npm install
```

3. Set up environment variables
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Start the development server
```bash
npm run dev
```

The server will start on `http://localhost:5000`

### Available Scripts

- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm test` - Run test suite
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage report
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues

## API Documentation

### Base URL
```
http://localhost:5000/api/v1
```

### Health Check
```
GET /health
```

### Authentication Endpoints
```
POST /api/v1/auth/register
POST /api/v1/auth/login
POST /api/v1/auth/refresh
POST /api/v1/auth/logout
```

### User Management
```
GET /api/v1/users/profile
PUT /api/v1/users/profile
GET /api/v1/users/addresses
POST /api/v1/users/addresses
```

### Driver Management
```
POST /api/v1/drivers/register
GET /api/v1/drivers/profile
PUT /api/v1/drivers/profile
POST /api/v1/drivers/documents
```

### Booking Management
```
POST /api/v1/bookings
GET /api/v1/bookings
GET /api/v1/bookings/:id
PUT /api/v1/bookings/:id/status
```

### Admin Dashboard
```
GET /api/v1/admin/analytics
GET /api/v1/admin/users
GET /api/v1/admin/drivers
GET /api/v1/admin/bookings
```

## Project Structure

```
src/
├── config/          # Configuration files
├── controllers/     # Route controllers
├── middleware/      # Custom middleware
├── models/          # Database models
├── routes/          # API routes
├── services/        # Business logic
├── utils/           # Utility functions
├── validators/      # Input validation
└── jobs/           # Background jobs

tests/
├── unit/           # Unit tests
├── integration/    # Integration tests
└── e2e/           # End-to-end tests
```

## Environment Variables

See `.env.example` for all required environment variables.

Key variables:
- `NODE_ENV` - Environment (development/production)
- `PORT` - Server port
- `MONGODB_URI` - MongoDB connection string
- `JWT_SECRET` - JWT secret key
- `REDIS_HOST` - Redis host
- `STRIPE_SECRET_KEY` - Stripe secret key

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the ISC License.
