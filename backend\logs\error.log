{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:45:14"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:45:15"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:45:15"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:45:15"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:45:16"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:45:17"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:45:18"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:45:20"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:45:23"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:45:25"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:45:27"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:45:29"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:53:01"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:53:01"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:53:01"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:53:02"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:53:02"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:53:03"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:53:05"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:53:07"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:53:09"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:53:11"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:53:13"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:53:15"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:53:17"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:53:19"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:53:21"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:53:24"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:53:26"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:53:28"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:53:30"}
{"cause":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{"localhost:27017":{"$clusterTime":null,"address":"localhost:27017","arbiters":[],"electionId":null,"error":{"beforeHandshake":false,"errorLabelSet":{}},"hosts":[],"iscryptd":false,"lastUpdateTime":5013653,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"}},"setName":null,"stale":false,"type":"Unknown"},"errorLabelSet":{},"level":"error","message":"Database connection failed: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{"localhost:27017":{"$clusterTime":null,"address":"localhost:27017","arbiters":[],"electionId":null,"error":{"beforeHandshake":false,"errorLabelSet":{}},"hosts":[],"iscryptd":false,"lastUpdateTime":5013653,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"}},"setName":null,"stale":false,"type":"Unknown"},"service":"logistics-platform","stack":"MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Fabaf Projects\\MERN\\Parter\\backend\\node_modules\\mongoose\\lib\\connection.js:1165:11)\n    at NativeConnection.openUri (D:\\Fabaf Projects\\MERN\\Parter\\backend\\node_modules\\mongoose\\lib\\connection.js:1096:11)\n    at async connectDB (D:\\Fabaf Projects\\MERN\\Parter\\backend\\src\\config\\database.js:6:18)","timestamp":"2025-08-07 09:53:31"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:57:28"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:57:29"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:57:29"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:57:29"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:57:30"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:57:30"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:57:32"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:57:34"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:57:36"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:57:39"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:57:41"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:57:43"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:57:45"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:57:47"}
{"code":"ECONNREFUSED","level":"error","message":"Redis client error:","service":"logistics-platform","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1118:18)\n    at afterConnectMultiple (node:net:1685:7)","timestamp":"2025-08-07 09:57:49"}
