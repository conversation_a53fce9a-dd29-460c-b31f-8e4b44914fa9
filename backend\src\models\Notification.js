const mongoose = require('mongoose');
const { NOTIFICATION_TYPES, NOTIFICATION_CHANNELS } = require('../config/constants');

const notificationSchema = new mongoose.Schema({
  recipient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  type: {
    type: String,
    enum: Object.values(NOTIFICATION_TYPES),
    required: true
  },
  title: {
    type: String,
    required: true,
    maxlength: [100, 'Title cannot exceed 100 characters']
  },
  message: {
    type: String,
    required: true,
    maxlength: [500, 'Message cannot exceed 500 characters']
  },
  data: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  channels: [{
    type: {
      type: String,
      enum: Object.values(NOTIFICATION_CHANNELS),
      required: true
    },
    status: {
      type: String,
      enum: ['pending', 'sent', 'delivered', 'failed', 'read'],
      default: 'pending'
    },
    sentAt: Date,
    deliveredAt: Date,
    readAt: Date,
    failureReason: String,
    externalId: String, // ID from external service (Twilio, SendGrid, etc.)
    metadata: mongoose.Schema.Types.Mixed
  }],
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal'
  },
  category: {
    type: String,
    enum: ['booking', 'payment', 'system', 'promotional', 'security'],
    required: true
  },
  relatedEntity: {
    entityType: {
      type: String,
      enum: ['booking', 'payment', 'user', 'driver']
    },
    entityId: mongoose.Schema.Types.ObjectId
  },
  isRead: {
    type: Boolean,
    default: false
  },
  readAt: Date,
  expiresAt: Date,
  scheduledFor: Date,
  template: {
    name: String,
    variables: mongoose.Schema.Types.Mixed
  },
  actionButtons: [{
    text: String,
    action: String,
    url: String,
    style: {
      type: String,
      enum: ['primary', 'secondary', 'danger'],
      default: 'primary'
    }
  }],
  isVisible: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
notificationSchema.index({ recipient: 1, createdAt: -1 });
notificationSchema.index({ type: 1 });
notificationSchema.index({ category: 1 });
notificationSchema.index({ priority: 1 });
notificationSchema.index({ isRead: 1 });
notificationSchema.index({ scheduledFor: 1 });
notificationSchema.index({ expiresAt: 1 });
notificationSchema.index({ 'relatedEntity.entityType': 1, 'relatedEntity.entityId': 1 });
notificationSchema.index({ 'channels.type': 1, 'channels.status': 1 });

// TTL index for expired notifications
notificationSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Virtual for overall delivery status
notificationSchema.virtual('deliveryStatus').get(function() {
  if (this.channels.length === 0) return 'pending';
  
  const statuses = this.channels.map(channel => channel.status);
  
  if (statuses.every(status => status === 'delivered' || status === 'read')) {
    return 'delivered';
  } else if (statuses.some(status => status === 'sent' || status === 'delivered' || status === 'read')) {
    return 'partial';
  } else if (statuses.every(status => status === 'failed')) {
    return 'failed';
  } else {
    return 'pending';
  }
});

// Virtual for is expired
notificationSchema.virtual('isExpired').get(function() {
  return this.expiresAt && this.expiresAt < new Date();
});

// Method to mark as read
notificationSchema.methods.markAsRead = function() {
  this.isRead = true;
  this.readAt = new Date();
  
  // Update in-app channel status
  const inAppChannel = this.channels.find(channel => channel.type === NOTIFICATION_CHANNELS.IN_APP);
  if (inAppChannel) {
    inAppChannel.status = 'read';
    inAppChannel.readAt = new Date();
  }
  
  return this.save();
};

// Method to update channel status
notificationSchema.methods.updateChannelStatus = function(channelType, status, metadata = {}) {
  const channel = this.channels.find(ch => ch.type === channelType);
  if (channel) {
    channel.status = status;
    
    const statusTimestamps = {
      'sent': 'sentAt',
      'delivered': 'deliveredAt',
      'read': 'readAt'
    };
    
    if (statusTimestamps[status]) {
      channel[statusTimestamps[status]] = new Date();
    }
    
    if (metadata.failureReason) {
      channel.failureReason = metadata.failureReason;
    }
    
    if (metadata.externalId) {
      channel.externalId = metadata.externalId;
    }
    
    if (metadata.metadata) {
      channel.metadata = metadata.metadata;
    }
  }
  
  return this.save();
};

// Method to add channel
notificationSchema.methods.addChannel = function(channelType, metadata = {}) {
  this.channels.push({
    type: channelType,
    status: 'pending',
    ...metadata
  });
  
  return this.save();
};

// Static method to get unread count
notificationSchema.statics.getUnreadCount = function(userId) {
  return this.countDocuments({
    recipient: userId,
    isRead: false,
    isVisible: true,
    $or: [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: new Date() } }
    ]
  });
};

// Static method to mark all as read
notificationSchema.statics.markAllAsRead = function(userId) {
  return this.updateMany(
    {
      recipient: userId,
      isRead: false,
      isVisible: true
    },
    {
      $set: {
        isRead: true,
        readAt: new Date()
      }
    }
  );
};

// Static method to get notifications with pagination
notificationSchema.statics.getNotifications = function(userId, options = {}) {
  const {
    page = 1,
    limit = 20,
    category,
    type,
    isRead,
    priority
  } = options;
  
  const query = {
    recipient: userId,
    isVisible: true,
    $or: [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: new Date() } }
    ]
  };
  
  if (category) query.category = category;
  if (type) query.type = type;
  if (typeof isRead === 'boolean') query.isRead = isRead;
  if (priority) query.priority = priority;
  
  return this.find(query)
    .sort({ createdAt: -1 })
    .limit(limit * 1)
    .skip((page - 1) * limit)
    .populate('relatedEntity.entityId');
};

// Static method to clean up old notifications
notificationSchema.statics.cleanupOldNotifications = function(daysOld = 30) {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysOld);
  
  return this.deleteMany({
    createdAt: { $lt: cutoffDate },
    isRead: true,
    priority: { $in: ['low', 'normal'] }
  });
};

module.exports = mongoose.model('Notification', notificationSchema);
