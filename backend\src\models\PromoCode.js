const mongoose = require('mongoose');

const promoCodeSchema = new mongoose.Schema({
  code: {
    type: String,
    required: true,
    unique: true,
    uppercase: true,
    trim: true,
    minlength: [3, 'Promo code must be at least 3 characters'],
    maxlength: [20, 'Promo code cannot exceed 20 characters']
  },
  title: {
    type: String,
    required: true,
    maxlength: [100, 'Title cannot exceed 100 characters']
  },
  description: {
    type: String,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  type: {
    type: String,
    enum: ['percentage', 'fixed_amount', 'free_delivery'],
    required: true
  },
  value: {
    type: Number,
    required: true,
    min: 0
  },
  maxDiscountAmount: {
    type: Number,
    min: 0
  },
  minOrderAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  currency: {
    type: String,
    default: 'USD',
    uppercase: true
  },
  usageLimit: {
    total: {
      type: Number,
      min: 1
    },
    perUser: {
      type: Number,
      default: 1,
      min: 1
    }
  },
  usageCount: {
    total: {
      type: Number,
      default: 0
    },
    byUser: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      count: {
        type: Number,
        default: 0
      },
      lastUsed: Date
    }]
  },
  validFrom: {
    type: Date,
    required: true
  },
  validUntil: {
    type: Date,
    required: true
  },
  applicableFor: {
    userTypes: [{
      type: String,
      enum: ['new_user', 'existing_user', 'premium_user']
    }],
    vehicleTypes: [{
      type: String,
      enum: ['bike', 'mini_truck', 'truck', 'van', 'courier']
    }],
    cities: [String],
    firstTimeUsers: {
      type: Boolean,
      default: false
    }
  },
  restrictions: {
    daysOfWeek: [{
      type: Number,
      min: 0,
      max: 6 // 0 = Sunday, 6 = Saturday
    }],
    timeSlots: [{
      start: String, // HH:MM format
      end: String    // HH:MM format
    }],
    excludedDates: [Date],
    minimumDistance: Number,
    maximumDistance: Number
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  approvedAt: Date,
  tags: [String],
  metadata: {
    campaign: String,
    source: String,
    medium: String,
    content: String
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
promoCodeSchema.index({ code: 1 });
promoCodeSchema.index({ isActive: 1, validFrom: 1, validUntil: 1 });
promoCodeSchema.index({ type: 1 });
promoCodeSchema.index({ createdBy: 1 });
promoCodeSchema.index({ 'applicableFor.userTypes': 1 });
promoCodeSchema.index({ 'applicableFor.vehicleTypes': 1 });
promoCodeSchema.index({ 'applicableFor.cities': 1 });
promoCodeSchema.index({ tags: 1 });

// Virtual for is expired
promoCodeSchema.virtual('isExpired').get(function() {
  return new Date() > this.validUntil;
});

// Virtual for is not yet valid
promoCodeSchema.virtual('isNotYetValid').get(function() {
  return new Date() < this.validFrom;
});

// Virtual for is currently valid
promoCodeSchema.virtual('isCurrentlyValid').get(function() {
  const now = new Date();
  return this.isActive && now >= this.validFrom && now <= this.validUntil;
});

// Virtual for usage percentage
promoCodeSchema.virtual('usagePercentage').get(function() {
  if (!this.usageLimit.total) return 0;
  return (this.usageCount.total / this.usageLimit.total) * 100;
});

// Virtual for remaining uses
promoCodeSchema.virtual('remainingUses').get(function() {
  if (!this.usageLimit.total) return Infinity;
  return Math.max(0, this.usageLimit.total - this.usageCount.total);
});

// Method to check if user can use this promo code
promoCodeSchema.methods.canUserUse = function(userId, userType, orderDetails = {}) {
  // Check if promo code is currently valid
  if (!this.isCurrentlyValid) {
    return { canUse: false, reason: 'Promo code is not currently valid' };
  }
  
  // Check total usage limit
  if (this.usageLimit.total && this.usageCount.total >= this.usageLimit.total) {
    return { canUse: false, reason: 'Promo code usage limit exceeded' };
  }
  
  // Check per-user usage limit
  const userUsage = this.usageCount.byUser.find(u => u.user.toString() === userId.toString());
  if (userUsage && userUsage.count >= this.usageLimit.perUser) {
    return { canUse: false, reason: 'You have already used this promo code maximum times' };
  }
  
  // Check minimum order amount
  if (orderDetails.amount && orderDetails.amount < this.minOrderAmount) {
    return { canUse: false, reason: `Minimum order amount is ${this.minOrderAmount}` };
  }
  
  // Check user type restrictions
  if (this.applicableFor.userTypes.length > 0 && !this.applicableFor.userTypes.includes(userType)) {
    return { canUse: false, reason: 'This promo code is not applicable for your user type' };
  }
  
  // Check vehicle type restrictions
  if (this.applicableFor.vehicleTypes.length > 0 && orderDetails.vehicleType && 
      !this.applicableFor.vehicleTypes.includes(orderDetails.vehicleType)) {
    return { canUse: false, reason: 'This promo code is not applicable for selected vehicle type' };
  }
  
  // Check city restrictions
  if (this.applicableFor.cities.length > 0 && orderDetails.city && 
      !this.applicableFor.cities.includes(orderDetails.city)) {
    return { canUse: false, reason: 'This promo code is not applicable in your city' };
  }
  
  // Check day of week restrictions
  if (this.restrictions.daysOfWeek.length > 0) {
    const currentDay = new Date().getDay();
    if (!this.restrictions.daysOfWeek.includes(currentDay)) {
      return { canUse: false, reason: 'This promo code is not valid today' };
    }
  }
  
  // Check time slot restrictions
  if (this.restrictions.timeSlots.length > 0) {
    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    
    const isInTimeSlot = this.restrictions.timeSlots.some(slot => {
      return currentTime >= slot.start && currentTime <= slot.end;
    });
    
    if (!isInTimeSlot) {
      return { canUse: false, reason: 'This promo code is not valid at this time' };
    }
  }
  
  // Check excluded dates
  if (this.restrictions.excludedDates.length > 0) {
    const today = new Date().toDateString();
    const isExcluded = this.restrictions.excludedDates.some(date => 
      new Date(date).toDateString() === today
    );
    
    if (isExcluded) {
      return { canUse: false, reason: 'This promo code is not valid today' };
    }
  }
  
  return { canUse: true };
};

// Method to calculate discount
promoCodeSchema.methods.calculateDiscount = function(orderAmount) {
  let discount = 0;
  
  switch (this.type) {
    case 'percentage':
      discount = (orderAmount * this.value) / 100;
      if (this.maxDiscountAmount) {
        discount = Math.min(discount, this.maxDiscountAmount);
      }
      break;
    case 'fixed_amount':
      discount = Math.min(this.value, orderAmount);
      break;
    case 'free_delivery':
      // This would need to be handled based on delivery fee calculation
      discount = this.value; // Assuming value contains the delivery fee amount
      break;
  }
  
  return Math.round(discount * 100) / 100; // Round to 2 decimal places
};

// Method to use promo code
promoCodeSchema.methods.use = function(userId) {
  // Increment total usage
  this.usageCount.total += 1;
  
  // Update user-specific usage
  const userUsage = this.usageCount.byUser.find(u => u.user.toString() === userId.toString());
  if (userUsage) {
    userUsage.count += 1;
    userUsage.lastUsed = new Date();
  } else {
    this.usageCount.byUser.push({
      user: userId,
      count: 1,
      lastUsed: new Date()
    });
  }
  
  return this.save();
};

// Static method to find valid promo codes for user
promoCodeSchema.statics.findValidForUser = function(userId, userType, orderDetails = {}) {
  const now = new Date();
  
  return this.find({
    isActive: true,
    validFrom: { $lte: now },
    validUntil: { $gte: now },
    $or: [
      { 'usageLimit.total': { $exists: false } },
      { $expr: { $lt: ['$usageCount.total', '$usageLimit.total'] } }
    ]
  }).then(promoCodes => {
    return promoCodes.filter(promoCode => {
      const canUse = promoCode.canUserUse(userId, userType, orderDetails);
      return canUse.canUse;
    });
  });
};

module.exports = mongoose.model('PromoCode', promoCodeSchema);
