{"name": "gcp-metadata", "version": "7.0.1", "description": "Get the metadata from a Google Cloud Platform environment", "repository": "googleapis/gcp-metadata", "main": "./build/src/index.js", "types": "./build/src/index.d.ts", "type": "commonjs", "files": ["build/src"], "scripts": {"compile": "cross-env NODE_OPTIONS=--max-old-space-size=8192 tsc -p .", "fix": "gts fix", "pretest": "npm run compile", "prepare": "npm run compile", "samples-test": "npm link && cd samples/ && npm link ../ && npm test && cd ../", "presystem-test": "npm run compile", "system-test": "mocha build/system-test --timeout 600000", "test": "c8 mocha --timeout=5000 build/test", "docs": "jsdoc -c .jsdoc.js", "lint": "gts check", "docs-test": "linkinator docs", "predocs-test": "npm run docs", "prelint": "cd samples; npm link ../; npm install", "clean": "gts clean", "precompile": "gts clean"}, "keywords": ["google cloud platform", "google cloud", "google", "app engine", "compute engine", "metadata server", "metadata"], "author": "Google LLC", "license": "Apache-2.0", "dependencies": {"gaxios": "^7.0.0", "google-logging-utils": "^1.0.0", "json-bigint": "^1.0.0"}, "devDependencies": {"@google-cloud/functions": "^3.6.0", "@types/json-bigint": "^1.0.4", "@types/mocha": "^10.0.9", "@types/ncp": "^2.0.8", "@types/node": "^22.9.0", "@types/sinon": "^17.0.3", "@types/tmp": "^0.2.6", "c8": "^10.1.2", "cross-env": "^7.0.3", "gcbuild": "^1.3.39", "gcx": "^2.0.27", "gts": "^6.0.2", "jsdoc": "^4.0.4", "jsdoc-fresh": "^3.0.0", "jsdoc-region-tag": "^3.0.0", "linkinator": "^6.1.2", "mocha": "^11.1.0", "ncp": "^2.0.0", "nock": "^14.0.1", "sinon": "^20.0.0", "tmp": "^0.2.3", "typescript": "^5.6.3"}, "engines": {"node": ">=18"}}