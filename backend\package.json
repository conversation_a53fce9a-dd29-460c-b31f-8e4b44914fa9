{"name": "logistics-platform-backend", "version": "1.0.0", "description": "Production-ready backend for comprehensive logistics and intra-city transportation booking platform", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "seed": "node scripts/seed.js seed", "seed:clear": "node scripts/seed.js clear", "seed:reset": "node scripts/seed.js reset"}, "keywords": ["logistics", "transportation", "booking", "express", "mongodb", "nodejs"], "author": "Logistics Platform Team", "license": "ISC", "type": "commonjs", "dependencies": {"axios": "^1.11.0", "bcryptjs": "^3.0.2", "bull": "^4.16.5", "compression": "^1.8.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.21.2", "express-async-errors": "^3.1.1", "express-fileupload": "^1.5.2", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "google-auth-library": "^10.2.1", "helmet": "^8.1.0", "joi": "^18.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.17.0", "morgan": "^1.10.1", "multer": "^2.0.2", "redis": "^5.8.0", "socket.io": "^4.8.1", "winston": "^3.17.0"}, "devDependencies": {"eslint": "^9.32.0", "jest": "^30.0.5", "nodemon": "^3.1.10", "supertest": "^7.1.4"}}