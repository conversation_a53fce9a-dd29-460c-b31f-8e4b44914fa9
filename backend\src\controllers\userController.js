const { User } = require('../models');
const { RESPONSE_MESSAGES } = require('../config/constants');
const logger = require('../config/logger');

/**
 * Update user profile
 */
const updateProfile = async (req, res) => {
  try {
    const userId = req.user._id;
    const updates = req.body;

    // Remove sensitive fields that shouldn't be updated via this endpoint
    delete updates.password;
    delete updates.email;
    delete updates.phone;
    delete updates.role;
    delete updates.isEmailVerified;
    delete updates.isPhoneVerified;
    delete updates.refreshTokens;

    const user = await User.findByIdAndUpdate(
      userId,
      { $set: updates },
      { new: true, runValidators: true }
    ).select('-password -refreshTokens');

    if (!user) {
      return res.status(404).json({
        success: false,
        error: RESPONSE_MESSAGES.NOT_FOUND
      });
    }

    logger.info(`Profile updated for user: ${user.email}`);

    res.json({
      success: true,
      message: RESPONSE_MESSAGES.UPDATED,
      data: { user }
    });
  } catch (error) {
    logger.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Upload profile picture
 */
const uploadProfilePicture = async (req, res) => {
  try {
    const userId = req.user._id;
    
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'Profile picture is required'
      });
    }

    // TODO: Upload to cloud storage (AWS S3, Cloudinary, etc.)
    const profilePictureUrl = `/uploads/profiles/${req.file.filename}`;

    const user = await User.findByIdAndUpdate(
      userId,
      { profilePicture: profilePictureUrl },
      { new: true }
    ).select('-password -refreshTokens');

    logger.info(`Profile picture updated for user: ${user.email}`);

    res.json({
      success: true,
      message: 'Profile picture updated successfully',
      data: { 
        user,
        profilePicture: profilePictureUrl
      }
    });
  } catch (error) {
    logger.error('Upload profile picture error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Get user addresses
 */
const getAddresses = async (req, res) => {
  try {
    const user = await User.findById(req.user._id).select('addresses');
    
    res.json({
      success: true,
      data: { addresses: user.addresses }
    });
  } catch (error) {
    logger.error('Get addresses error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Add new address
 */
const addAddress = async (req, res) => {
  try {
    const userId = req.user._id;
    const addressData = req.body;

    const user = await User.findById(userId);
    
    // If this is set as default, unset other default addresses
    if (addressData.isDefault) {
      user.addresses.forEach(address => {
        address.isDefault = false;
      });
    }

    user.addresses.push(addressData);
    await user.save();

    const newAddress = user.addresses[user.addresses.length - 1];

    logger.info(`Address added for user: ${user.email}`);

    res.status(201).json({
      success: true,
      message: 'Address added successfully',
      data: { address: newAddress }
    });
  } catch (error) {
    logger.error('Add address error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Update address
 */
const updateAddress = async (req, res) => {
  try {
    const userId = req.user._id;
    const addressId = req.params.addressId;
    const updates = req.body;

    const user = await User.findById(userId);
    const address = user.addresses.id(addressId);

    if (!address) {
      return res.status(404).json({
        success: false,
        error: 'Address not found'
      });
    }

    // If this is set as default, unset other default addresses
    if (updates.isDefault) {
      user.addresses.forEach(addr => {
        if (addr._id.toString() !== addressId) {
          addr.isDefault = false;
        }
      });
    }

    // Update address fields
    Object.keys(updates).forEach(key => {
      address[key] = updates[key];
    });

    await user.save();

    logger.info(`Address updated for user: ${user.email}`);

    res.json({
      success: true,
      message: 'Address updated successfully',
      data: { address }
    });
  } catch (error) {
    logger.error('Update address error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Delete address
 */
const deleteAddress = async (req, res) => {
  try {
    const userId = req.user._id;
    const addressId = req.params.addressId;

    const user = await User.findById(userId);
    const address = user.addresses.id(addressId);

    if (!address) {
      return res.status(404).json({
        success: false,
        error: 'Address not found'
      });
    }

    user.addresses.pull(addressId);
    await user.save();

    logger.info(`Address deleted for user: ${user.email}`);

    res.json({
      success: true,
      message: 'Address deleted successfully'
    });
  } catch (error) {
    logger.error('Delete address error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Set default address
 */
const setDefaultAddress = async (req, res) => {
  try {
    const userId = req.user._id;
    const addressId = req.params.addressId;

    const user = await User.findById(userId);
    const address = user.addresses.id(addressId);

    if (!address) {
      return res.status(404).json({
        success: false,
        error: 'Address not found'
      });
    }

    // Unset all default addresses
    user.addresses.forEach(addr => {
      addr.isDefault = false;
    });

    // Set this address as default
    address.isDefault = true;
    await user.save();

    logger.info(`Default address set for user: ${user.email}`);

    res.json({
      success: true,
      message: 'Default address updated successfully',
      data: { address }
    });
  } catch (error) {
    logger.error('Set default address error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Change password
 */
const changePassword = async (req, res) => {
  try {
    const userId = req.user._id;
    const { currentPassword, newPassword } = req.body;

    const user = await User.findById(userId).select('+password');
    
    // Verify current password
    const isCurrentPasswordValid = await user.comparePassword(currentPassword);
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        error: 'Current password is incorrect'
      });
    }

    // Update password
    user.password = newPassword;
    await user.save();

    logger.info(`Password changed for user: ${user.email}`);

    res.json({
      success: true,
      message: 'Password changed successfully'
    });
  } catch (error) {
    logger.error('Change password error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Update notification preferences
 */
const updateNotificationPreferences = async (req, res) => {
  try {
    const userId = req.user._id;
    const preferences = req.body;

    const user = await User.findByIdAndUpdate(
      userId,
      { 
        $set: { 
          'preferences.notifications': preferences 
        } 
      },
      { new: true, runValidators: true }
    ).select('-password -refreshTokens');

    logger.info(`Notification preferences updated for user: ${user.email}`);

    res.json({
      success: true,
      message: 'Notification preferences updated successfully',
      data: { 
        preferences: user.preferences.notifications 
      }
    });
  } catch (error) {
    logger.error('Update notification preferences error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Deactivate account
 */
const deactivateAccount = async (req, res) => {
  try {
    const userId = req.user._id;
    const { password } = req.body;

    const user = await User.findById(userId).select('+password');
    
    // Verify password
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      return res.status(400).json({
        success: false,
        error: 'Password is incorrect'
      });
    }

    // Deactivate account
    user.isActive = false;
    user.refreshTokens = []; // Clear all refresh tokens
    await user.save();

    logger.info(`Account deactivated for user: ${user.email}`);

    res.json({
      success: true,
      message: 'Account deactivated successfully'
    });
  } catch (error) {
    logger.error('Deactivate account error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

module.exports = {
  updateProfile,
  uploadProfilePicture,
  getAddresses,
  addAddress,
  updateAddress,
  deleteAddress,
  setDefaultAddress,
  changePassword,
  updateNotificationPreferences,
  deactivateAccount
};
