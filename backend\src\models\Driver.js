const mongoose = require('mongoose');
const { DRIVER_STATUS, DRIVER_VERIFICATION_STATUS, VEHICLE_TYPES } = require('../config/constants');

const documentSchema = new mongoose.Schema({
  type: {
    type: String,
    required: true,
    enum: ['license', 'registration', 'insurance', 'identity', 'address_proof']
  },
  url: {
    type: String,
    required: true
  },
  fileName: String,
  uploadedAt: {
    type: Date,
    default: Date.now
  },
  verificationStatus: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending'
  },
  rejectionReason: String,
  expiryDate: Date
});

const vehicleSchema = new mongoose.Schema({
  type: {
    type: String,
    required: true,
    enum: Object.values(VEHICLE_TYPES)
  },
  make: {
    type: String,
    required: true
  },
  model: {
    type: String,
    required: true
  },
  year: {
    type: Number,
    required: true,
    min: 1990,
    max: new Date().getFullYear() + 1
  },
  color: {
    type: String,
    required: true
  },
  licensePlate: {
    type: String,
    required: true,
    unique: true,
    uppercase: true
  },
  capacity: {
    weight: Number, // in kg
    volume: Number, // in cubic meters
    passengers: Number
  },
  features: [String], // e.g., ['air_conditioning', 'gps', 'refrigerated']
  images: [String],
  isActive: {
    type: Boolean,
    default: true
  }
}, { timestamps: true });

const bankDetailsSchema = new mongoose.Schema({
  accountHolderName: {
    type: String,
    required: true
  },
  accountNumber: {
    type: String,
    required: true
  },
  routingNumber: String,
  bankName: {
    type: String,
    required: true
  },
  branchCode: String,
  isVerified: {
    type: Boolean,
    default: false
  }
});

const driverSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  driverLicense: {
    number: {
      type: String,
      required: true,
      unique: true
    },
    expiryDate: {
      type: Date,
      required: true
    },
    class: String, // e.g., 'CDL-A', 'CDL-B', 'Regular'
    state: String
  },
  vehicles: [vehicleSchema],
  documents: [documentSchema],
  bankDetails: bankDetailsSchema,
  verificationStatus: {
    type: String,
    enum: Object.values(DRIVER_VERIFICATION_STATUS),
    default: DRIVER_VERIFICATION_STATUS.PENDING
  },
  verificationNotes: String,
  verifiedAt: Date,
  verifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  status: {
    type: String,
    enum: Object.values(DRIVER_STATUS),
    default: DRIVER_STATUS.OFFLINE
  },
  currentLocation: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point'
    },
    coordinates: {
      type: [Number], // [longitude, latitude]
      default: [0, 0]
    }
  },
  lastLocationUpdate: Date,
  workingHours: {
    monday: { start: String, end: String, isWorking: Boolean },
    tuesday: { start: String, end: String, isWorking: Boolean },
    wednesday: { start: String, end: String, isWorking: Boolean },
    thursday: { start: String, end: String, isWorking: Boolean },
    friday: { start: String, end: String, isWorking: Boolean },
    saturday: { start: String, end: String, isWorking: Boolean },
    sunday: { start: String, end: String, isWorking: Boolean }
  },
  ratings: {
    average: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    count: {
      type: Number,
      default: 0
    }
  },
  earnings: {
    totalEarnings: {
      type: Number,
      default: 0
    },
    currentBalance: {
      type: Number,
      default: 0
    },
    totalWithdrawn: {
      type: Number,
      default: 0
    }
  },
  statistics: {
    totalTrips: {
      type: Number,
      default: 0
    },
    completedTrips: {
      type: Number,
      default: 0
    },
    cancelledTrips: {
      type: Number,
      default: 0
    },
    totalDistance: {
      type: Number,
      default: 0
    },
    totalDrivingTime: {
      type: Number,
      default: 0
    },
    acceptanceRate: {
      type: Number,
      default: 0
    }
  },
  preferences: {
    maxDistance: {
      type: Number,
      default: 50 // km
    },
    preferredVehicleTypes: [String],
    autoAcceptBookings: {
      type: Boolean,
      default: false
    },
    notifications: {
      newBookings: {
        type: Boolean,
        default: true
      },
      paymentUpdates: {
        type: Boolean,
        default: true
      },
      promotions: {
        type: Boolean,
        default: true
      }
    }
  },
  emergencyContact: {
    name: String,
    phone: String,
    relationship: String
  },
  isActive: {
    type: Boolean,
    default: true
  },
  suspendedUntil: Date,
  suspensionReason: String,
  onboardingCompletedAt: Date,
  lastActiveAt: Date
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
driverSchema.index({ user: 1 });
driverSchema.index({ 'driverLicense.number': 1 });
driverSchema.index({ verificationStatus: 1 });
driverSchema.index({ status: 1 });
driverSchema.index({ currentLocation: '2dsphere' });
driverSchema.index({ 'vehicles.licensePlate': 1 });
driverSchema.index({ 'ratings.average': -1 });
driverSchema.index({ isActive: 1 });
driverSchema.index({ createdAt: -1 });

// Virtual for completion percentage
driverSchema.virtual('profileCompletionPercentage').get(function() {
  let completed = 0;
  let total = 10;
  
  if (this.driverLicense.number) completed++;
  if (this.vehicles.length > 0) completed++;
  if (this.documents.length >= 3) completed++;
  if (this.bankDetails.accountNumber) completed++;
  if (this.emergencyContact.name) completed++;
  if (this.workingHours.monday) completed++;
  if (this.preferences.maxDistance) completed++;
  if (this.verificationStatus === DRIVER_VERIFICATION_STATUS.APPROVED) completed += 3;
  
  return Math.round((completed / total) * 100);
});

// Virtual for active vehicle
driverSchema.virtual('activeVehicle').get(function() {
  return this.vehicles.find(vehicle => vehicle.isActive);
});

// Method to update location
driverSchema.methods.updateLocation = function(longitude, latitude) {
  this.currentLocation = {
    type: 'Point',
    coordinates: [longitude, latitude]
  };
  this.lastLocationUpdate = new Date();
  return this.save();
};

// Method to calculate distance from a point
driverSchema.methods.distanceFrom = function(longitude, latitude) {
  const R = 6371; // Earth's radius in km
  const dLat = (latitude - this.currentLocation.coordinates[1]) * Math.PI / 180;
  const dLon = (longitude - this.currentLocation.coordinates[0]) * Math.PI / 180;
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(this.currentLocation.coordinates[1] * Math.PI / 180) * Math.cos(latitude * Math.PI / 180) *
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
};

// Method to update ratings
driverSchema.methods.updateRating = function(newRating) {
  const totalRating = (this.ratings.average * this.ratings.count) + newRating;
  this.ratings.count += 1;
  this.ratings.average = totalRating / this.ratings.count;
  return this.save();
};

module.exports = mongoose.model('Driver', driverSchema);
