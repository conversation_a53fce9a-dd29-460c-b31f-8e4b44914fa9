const mongoose = require('mongoose');

const reviewSchema = new mongoose.Schema({
  booking: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Booking',
    required: true
  },
  reviewer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  reviewee: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  reviewerType: {
    type: String,
    enum: ['user', 'driver'],
    required: true
  },
  revieweeType: {
    type: String,
    enum: ['user', 'driver'],
    required: true
  },
  rating: {
    type: Number,
    required: true,
    min: 1,
    max: 5
  },
  comment: {
    type: String,
    maxlength: [500, 'Comment cannot exceed 500 characters'],
    trim: true
  },
  categories: {
    punctuality: {
      type: Number,
      min: 1,
      max: 5
    },
    communication: {
      type: Number,
      min: 1,
      max: 5
    },
    professionalism: {
      type: Number,
      min: 1,
      max: 5
    },
    vehicleCondition: {
      type: Number,
      min: 1,
      max: 5
    },
    safety: {
      type: Number,
      min: 1,
      max: 5
    }
  },
  tags: [String], // e.g., ['friendly', 'fast', 'careful', 'rude', 'late']
  isAnonymous: {
    type: Boolean,
    default: false
  },
  isVerified: {
    type: Boolean,
    default: true
  },
  moderationStatus: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'flagged'],
    default: 'approved'
  },
  moderationNotes: String,
  moderatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  moderatedAt: Date,
  isHelpful: {
    helpfulCount: {
      type: Number,
      default: 0
    },
    notHelpfulCount: {
      type: Number,
      default: 0
    }
  },
  response: {
    comment: String,
    respondedAt: Date,
    respondedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  },
  reportedBy: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    reason: String,
    reportedAt: {
      type: Date,
      default: Date.now
    }
  }],
  isVisible: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
reviewSchema.index({ booking: 1 });
reviewSchema.index({ reviewer: 1, createdAt: -1 });
reviewSchema.index({ reviewee: 1, createdAt: -1 });
reviewSchema.index({ reviewerType: 1, revieweeType: 1 });
reviewSchema.index({ rating: -1 });
reviewSchema.index({ moderationStatus: 1 });
reviewSchema.index({ isVisible: 1 });
reviewSchema.index({ createdAt: -1 });

// Compound index for preventing duplicate reviews
reviewSchema.index({ booking: 1, reviewer: 1, reviewee: 1 }, { unique: true });

// Virtual for helpfulness ratio
reviewSchema.virtual('helpfulnessRatio').get(function() {
  const total = this.isHelpful.helpfulCount + this.isHelpful.notHelpfulCount;
  return total > 0 ? this.isHelpful.helpfulCount / total : 0;
});

// Virtual for average category rating
reviewSchema.virtual('averageCategoryRating').get(function() {
  const categories = this.categories;
  const ratings = Object.values(categories).filter(rating => rating != null);
  return ratings.length > 0 ? ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length : null;
});

// Method to mark as helpful/not helpful
reviewSchema.methods.markHelpful = function(isHelpful) {
  if (isHelpful) {
    this.isHelpful.helpfulCount += 1;
  } else {
    this.isHelpful.notHelpfulCount += 1;
  }
  return this.save();
};

// Method to add response
reviewSchema.methods.addResponse = function(comment, respondedBy) {
  this.response = {
    comment,
    respondedAt: new Date(),
    respondedBy
  };
  return this.save();
};

// Method to report review
reviewSchema.methods.reportReview = function(userId, reason) {
  this.reportedBy.push({
    user: userId,
    reason,
    reportedAt: new Date()
  });
  
  // Auto-flag if reported by multiple users
  if (this.reportedBy.length >= 3) {
    this.moderationStatus = 'flagged';
  }
  
  return this.save();
};

// Static method to get average rating for a user/driver
reviewSchema.statics.getAverageRating = function(revieweeId, revieweeType) {
  return this.aggregate([
    {
      $match: {
        reviewee: revieweeId,
        revieweeType: revieweeType,
        moderationStatus: 'approved',
        isVisible: true
      }
    },
    {
      $group: {
        _id: null,
        averageRating: { $avg: '$rating' },
        totalReviews: { $sum: 1 },
        ratingDistribution: {
          $push: '$rating'
        }
      }
    },
    {
      $project: {
        _id: 0,
        averageRating: { $round: ['$averageRating', 2] },
        totalReviews: 1,
        ratingDistribution: {
          $reduce: {
            input: [1, 2, 3, 4, 5],
            initialValue: {},
            in: {
              $mergeObjects: [
                '$$value',
                {
                  $arrayToObject: [[
                    { k: { $toString: '$$this' }, v: {
                      $size: {
                        $filter: {
                          input: '$ratingDistribution',
                          cond: { $eq: ['$$item', '$$this'] }
                        }
                      }
                    }}
                  ]]
                }
              ]
            }
          }
        }
      }
    }
  ]);
};

// Static method to get review statistics
reviewSchema.statics.getStatistics = function(startDate, endDate, filters = {}) {
  const matchStage = {
    createdAt: { $gte: startDate, $lte: endDate },
    moderationStatus: 'approved',
    isVisible: true,
    ...filters
  };
  
  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalReviews: { $sum: 1 },
        averageRating: { $avg: '$rating' },
        ratingDistribution: {
          $push: '$rating'
        },
        totalWithComments: {
          $sum: { $cond: [{ $ne: ['$comment', ''] }, 1, 0] }
        }
      }
    }
  ]);
};

module.exports = mongoose.model('Review', reviewSchema);
