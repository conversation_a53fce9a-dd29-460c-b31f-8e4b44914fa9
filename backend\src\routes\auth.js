const express = require('express');
const router = express.Router();

// Import controllers
const {
  register,
  login,
  refresh,
  logout,
  logoutAll,
  getProfile,
  verifyEmail,
  verifyPhone,
  resendEmailVerification,
  resendPhoneVerification,
  googleAuth,
  facebookAuth
} = require('../controllers/authController');

// Import middleware
const {
  authenticate,
  authRateLimit,
  otpRateLimit
} = require('../middleware/auth');

// Import validators
const {
  validate,
  validateParams,
  registerValidation,
  loginValidation,
  refreshTokenValidation,
  phoneVerificationValidation,
  emailVerificationValidation,
  googleAuthValidation,
  facebookAuthValidation
} = require('../validators/authValidator');

/**
 * @route   POST /api/v1/auth/register
 * @desc    Register a new user
 * @access  Public
 */
router.post('/register',
  authRateLimit,
  validate(registerValidation),
  register
);

/**
 * @route   POST /api/v1/auth/login
 * @desc    Login user
 * @access  Public
 */
router.post('/login',
  authRateLimit,
  validate(loginValidation),
  login
);

/**
 * @route   POST /api/v1/auth/refresh
 * @desc    Refresh access token
 * @access  Public
 */
router.post('/refresh',
  validate(refreshTokenValidation),
  refresh
);

/**
 * @route   POST /api/v1/auth/logout
 * @desc    Logout user (revoke refresh token)
 * @access  Private
 */
router.post('/logout',
  authenticate,
  logout
);

/**
 * @route   POST /api/v1/auth/logout-all
 * @desc    Logout from all devices
 * @access  Private
 */
router.post('/logout-all',
  authenticate,
  logoutAll
);

/**
 * @route   GET /api/v1/auth/profile
 * @desc    Get current user profile
 * @access  Private
 */
router.get('/profile',
  authenticate,
  getProfile
);

/**
 * @route   GET /api/v1/auth/verify-email/:token
 * @desc    Verify email address
 * @access  Public
 */
router.get('/verify-email/:token',
  validateParams(emailVerificationValidation),
  verifyEmail
);

/**
 * @route   POST /api/v1/auth/verify-phone
 * @desc    Verify phone number with OTP
 * @access  Private
 */
router.post('/verify-phone',
  authenticate,
  validate(phoneVerificationValidation),
  verifyPhone
);

/**
 * @route   POST /api/v1/auth/resend-email-verification
 * @desc    Resend email verification
 * @access  Private
 */
router.post('/resend-email-verification',
  authenticate,
  otpRateLimit,
  resendEmailVerification
);

/**
 * @route   POST /api/v1/auth/resend-phone-verification
 * @desc    Resend phone verification OTP
 * @access  Private
 */
router.post('/resend-phone-verification',
  authenticate,
  otpRateLimit,
  resendPhoneVerification
);

/**
 * @route   POST /api/v1/auth/google
 * @desc    Google OAuth authentication
 * @access  Public
 */
router.post('/google',
  authRateLimit,
  validate(googleAuthValidation),
  googleAuth
);

/**
 * @route   POST /api/v1/auth/facebook
 * @desc    Facebook OAuth authentication
 * @access  Public
 */
router.post('/facebook',
  authRateLimit,
  validate(facebookAuthValidation),
  facebookAuth
);

module.exports = router;
