const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { User } = require('../models');
const logger = require('../config/logger');

/**
 * Generate JWT access token
 * @param {Object} payload - Token payload
 * @returns {String} JWT token
 */
const generateAccessToken = (payload) => {
  return jwt.sign(payload, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRE || '15m',
    issuer: 'logistics-platform',
    audience: 'logistics-platform-users'
  });
};

/**
 * Generate JWT refresh token
 * @param {Object} payload - Token payload
 * @returns {String} JWT refresh token
 */
const generateRefreshToken = (payload) => {
  return jwt.sign(payload, process.env.JWT_REFRESH_SECRET, {
    expiresIn: process.env.JWT_REFRESH_EXPIRE || '7d',
    issuer: 'logistics-platform',
    audience: 'logistics-platform-users'
  });
};

/**
 * Verify JWT token
 * @param {String} token - JWT token
 * @param {String} secret - JWT secret
 * @returns {Object} Decoded token payload
 */
const verifyToken = (token, secret) => {
  try {
    return jwt.verify(token, secret, {
      issuer: 'logistics-platform',
      audience: 'logistics-platform-users'
    });
  } catch (error) {
    throw error;
  }
};

/**
 * Generate token pair (access + refresh)
 * @param {Object} user - User object
 * @param {String} deviceInfo - Device information
 * @returns {Object} Token pair
 */
const generateTokenPair = async (user, deviceInfo = 'unknown') => {
  const payload = {
    userId: user._id,
    email: user.email,
    role: user.role,
    isEmailVerified: user.isEmailVerified,
    isPhoneVerified: user.isPhoneVerified
  };

  const accessToken = generateAccessToken(payload);
  const refreshToken = generateRefreshToken({ userId: user._id });

  // Store refresh token in database
  const refreshTokenData = {
    token: refreshToken,
    createdAt: new Date(),
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    deviceInfo
  };

  // Add refresh token to user's refresh tokens array
  user.refreshTokens.push(refreshTokenData);
  
  // Keep only last 5 refresh tokens per user
  if (user.refreshTokens.length > 5) {
    user.refreshTokens = user.refreshTokens.slice(-5);
  }

  await user.save();

  return {
    accessToken,
    refreshToken,
    expiresIn: process.env.JWT_EXPIRE || '15m'
  };
};

/**
 * Refresh access token using refresh token
 * @param {String} refreshToken - Refresh token
 * @returns {Object} New token pair
 */
const refreshAccessToken = async (refreshToken) => {
  try {
    // Verify refresh token
    const decoded = verifyToken(refreshToken, process.env.JWT_REFRESH_SECRET);
    
    // Find user and check if refresh token exists
    const user = await User.findById(decoded.userId);
    if (!user) {
      throw new Error('User not found');
    }

    const tokenData = user.refreshTokens.find(t => t.token === refreshToken);
    if (!tokenData) {
      throw new Error('Invalid refresh token');
    }

    // Check if token is expired
    if (tokenData.expiresAt < new Date()) {
      // Remove expired token
      user.refreshTokens = user.refreshTokens.filter(t => t.token !== refreshToken);
      await user.save();
      throw new Error('Refresh token expired');
    }

    // Generate new token pair
    const newTokenPair = await generateTokenPair(user, tokenData.deviceInfo);

    // Remove old refresh token
    user.refreshTokens = user.refreshTokens.filter(t => t.token !== refreshToken);
    await user.save();

    return newTokenPair;
  } catch (error) {
    logger.error('Token refresh failed:', error);
    throw error;
  }
};

/**
 * Revoke refresh token
 * @param {String} userId - User ID
 * @param {String} refreshToken - Refresh token to revoke
 */
const revokeRefreshToken = async (userId, refreshToken) => {
  try {
    const user = await User.findById(userId);
    if (user) {
      user.refreshTokens = user.refreshTokens.filter(t => t.token !== refreshToken);
      await user.save();
    }
  } catch (error) {
    logger.error('Token revocation failed:', error);
    throw error;
  }
};

/**
 * Revoke all refresh tokens for a user
 * @param {String} userId - User ID
 */
const revokeAllRefreshTokens = async (userId) => {
  try {
    const user = await User.findById(userId);
    if (user) {
      user.refreshTokens = [];
      await user.save();
    }
  } catch (error) {
    logger.error('All tokens revocation failed:', error);
    throw error;
  }
};

/**
 * Generate random token for email/phone verification
 * @returns {String} Random token
 */
const generateVerificationToken = () => {
  return crypto.randomBytes(32).toString('hex');
};

/**
 * Generate OTP for phone verification
 * @returns {String} 6-digit OTP
 */
const generateOTP = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

/**
 * Hash token for storage
 * @param {String} token - Token to hash
 * @returns {String} Hashed token
 */
const hashToken = (token) => {
  return crypto.createHash('sha256').update(token).digest('hex');
};

/**
 * Clean up expired refresh tokens
 */
const cleanupExpiredTokens = async () => {
  try {
    const users = await User.find({
      'refreshTokens.expiresAt': { $lt: new Date() }
    });

    for (const user of users) {
      user.refreshTokens = user.refreshTokens.filter(
        token => token.expiresAt >= new Date()
      );
      await user.save();
    }

    logger.info(`Cleaned up expired tokens for ${users.length} users`);
  } catch (error) {
    logger.error('Token cleanup failed:', error);
  }
};

/**
 * Extract token from Authorization header
 * @param {String} authHeader - Authorization header value
 * @returns {String|null} Extracted token
 */
const extractTokenFromHeader = (authHeader) => {
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  return null;
};

module.exports = {
  generateAccessToken,
  generateRefreshToken,
  verifyToken,
  generateTokenPair,
  refreshAccessToken,
  revokeRefreshToken,
  revokeAllRefreshTokens,
  generateVerificationToken,
  generateOTP,
  hashToken,
  cleanupExpiredTokens,
  extractTokenFromHeader
};
