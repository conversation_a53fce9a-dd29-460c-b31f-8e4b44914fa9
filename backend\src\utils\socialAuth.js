const { OAuth2Client } = require('google-auth-library');
const axios = require('axios');
const { User } = require('../models');
const { generateTokenPair } = require('./jwt');
const { USER_ROLES } = require('../config/constants');
const logger = require('../config/logger');

// Initialize Google OAuth client
const googleClient = new OAuth2Client(
  process.env.GOOGLE_CLIENT_ID,
  process.env.GOOGLE_CLIENT_SECRET
);

/**
 * Verify Google ID token and get user info
 * @param {String} idToken - Google ID token
 * @returns {Object} User information from Google
 */
const verifyGoogleToken = async (idToken) => {
  try {
    const ticket = await googleClient.verifyIdToken({
      idToken,
      audience: process.env.GOOGLE_CLIENT_ID
    });

    const payload = ticket.getPayload();
    
    return {
      id: payload.sub,
      email: payload.email,
      firstName: payload.given_name,
      lastName: payload.family_name,
      profilePicture: payload.picture,
      isEmailVerified: payload.email_verified
    };
  } catch (error) {
    logger.error('Google token verification failed:', error);
    throw new Error('Invalid Google token');
  }
};

/**
 * Verify Facebook access token and get user info
 * @param {String} accessToken - Facebook access token
 * @returns {Object} User information from Facebook
 */
const verifyFacebookToken = async (accessToken) => {
  try {
    // Verify token with Facebook
    const tokenResponse = await axios.get(
      `https://graph.facebook.com/me?access_token=${accessToken}&fields=id,email,first_name,last_name,picture`
    );

    const userData = tokenResponse.data;

    if (!userData.id) {
      throw new Error('Invalid Facebook token');
    }

    return {
      id: userData.id,
      email: userData.email,
      firstName: userData.first_name,
      lastName: userData.last_name,
      profilePicture: userData.picture?.data?.url,
      isEmailVerified: true // Facebook emails are considered verified
    };
  } catch (error) {
    logger.error('Facebook token verification failed:', error);
    throw new Error('Invalid Facebook token');
  }
};

/**
 * Handle Google OAuth login/registration
 * @param {String} idToken - Google ID token
 * @param {String} deviceInfo - Device information
 * @returns {Object} User data and tokens
 */
const handleGoogleAuth = async (idToken, deviceInfo = 'unknown') => {
  try {
    const googleUser = await verifyGoogleToken(idToken);
    
    // Check if user exists with this Google ID
    let user = await User.findOne({
      'socialAuth.google.id': googleUser.id
    });

    if (user) {
      // User exists, update last login and generate tokens
      user.lastLogin = new Date();
      await user.save();

      const tokens = await generateTokenPair(user, deviceInfo);

      logger.info(`Google login successful for user: ${user.email}`);

      return {
        user: {
          id: user._id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phone: user.phone,
          role: user.role,
          isEmailVerified: user.isEmailVerified,
          isPhoneVerified: user.isPhoneVerified,
          profilePicture: user.profilePicture,
          lastLogin: user.lastLogin
        },
        tokens,
        isNewUser: false
      };
    }

    // Check if user exists with this email
    user = await User.findOne({ email: googleUser.email });

    if (user) {
      // Link Google account to existing user
      user.socialAuth.google = {
        id: googleUser.id,
        email: googleUser.email
      };
      user.isEmailVerified = true; // Google emails are verified
      user.lastLogin = new Date();
      
      if (!user.profilePicture && googleUser.profilePicture) {
        user.profilePicture = googleUser.profilePicture;
      }
      
      await user.save();

      const tokens = await generateTokenPair(user, deviceInfo);

      logger.info(`Google account linked for existing user: ${user.email}`);

      return {
        user: {
          id: user._id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phone: user.phone,
          role: user.role,
          isEmailVerified: user.isEmailVerified,
          isPhoneVerified: user.isPhoneVerified,
          profilePicture: user.profilePicture,
          lastLogin: user.lastLogin
        },
        tokens,
        isNewUser: false
      };
    }

    // Create new user
    user = new User({
      firstName: googleUser.firstName,
      lastName: googleUser.lastName,
      email: googleUser.email,
      phone: '', // Will need to be added later
      password: Math.random().toString(36).slice(-8), // Random password
      role: USER_ROLES.USER,
      isEmailVerified: googleUser.isEmailVerified,
      profilePicture: googleUser.profilePicture,
      socialAuth: {
        google: {
          id: googleUser.id,
          email: googleUser.email
        }
      }
    });

    await user.save();

    const tokens = await generateTokenPair(user, deviceInfo);

    logger.info(`New user created via Google: ${user.email}`);

    return {
      user: {
        id: user._id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone,
        role: user.role,
        isEmailVerified: user.isEmailVerified,
        isPhoneVerified: user.isPhoneVerified,
        profilePicture: user.profilePicture,
        lastLogin: user.lastLogin
      },
      tokens,
      isNewUser: true
    };
  } catch (error) {
    logger.error('Google authentication error:', error);
    throw error;
  }
};

/**
 * Handle Facebook OAuth login/registration
 * @param {String} accessToken - Facebook access token
 * @param {String} deviceInfo - Device information
 * @returns {Object} User data and tokens
 */
const handleFacebookAuth = async (accessToken, deviceInfo = 'unknown') => {
  try {
    const facebookUser = await verifyFacebookToken(accessToken);
    
    // Check if user exists with this Facebook ID
    let user = await User.findOne({
      'socialAuth.facebook.id': facebookUser.id
    });

    if (user) {
      // User exists, update last login and generate tokens
      user.lastLogin = new Date();
      await user.save();

      const tokens = await generateTokenPair(user, deviceInfo);

      logger.info(`Facebook login successful for user: ${user.email}`);

      return {
        user: {
          id: user._id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phone: user.phone,
          role: user.role,
          isEmailVerified: user.isEmailVerified,
          isPhoneVerified: user.isPhoneVerified,
          profilePicture: user.profilePicture,
          lastLogin: user.lastLogin
        },
        tokens,
        isNewUser: false
      };
    }

    // Check if user exists with this email
    if (facebookUser.email) {
      user = await User.findOne({ email: facebookUser.email });

      if (user) {
        // Link Facebook account to existing user
        user.socialAuth.facebook = {
          id: facebookUser.id,
          email: facebookUser.email
        };
        user.isEmailVerified = true;
        user.lastLogin = new Date();
        
        if (!user.profilePicture && facebookUser.profilePicture) {
          user.profilePicture = facebookUser.profilePicture;
        }
        
        await user.save();

        const tokens = await generateTokenPair(user, deviceInfo);

        logger.info(`Facebook account linked for existing user: ${user.email}`);

        return {
          user: {
            id: user._id,
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
            phone: user.phone,
            role: user.role,
            isEmailVerified: user.isEmailVerified,
            isPhoneVerified: user.isPhoneVerified,
            profilePicture: user.profilePicture,
            lastLogin: user.lastLogin
          },
          tokens,
          isNewUser: false
        };
      }
    }

    // Create new user
    user = new User({
      firstName: facebookUser.firstName,
      lastName: facebookUser.lastName,
      email: facebookUser.email || '', // Facebook might not provide email
      phone: '', // Will need to be added later
      password: Math.random().toString(36).slice(-8), // Random password
      role: USER_ROLES.USER,
      isEmailVerified: facebookUser.isEmailVerified,
      profilePicture: facebookUser.profilePicture,
      socialAuth: {
        facebook: {
          id: facebookUser.id,
          email: facebookUser.email
        }
      }
    });

    await user.save();

    const tokens = await generateTokenPair(user, deviceInfo);

    logger.info(`New user created via Facebook: ${user.email || user._id}`);

    return {
      user: {
        id: user._id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone,
        role: user.role,
        isEmailVerified: user.isEmailVerified,
        isPhoneVerified: user.isPhoneVerified,
        profilePicture: user.profilePicture,
        lastLogin: user.lastLogin
      },
      tokens,
      isNewUser: true
    };
  } catch (error) {
    logger.error('Facebook authentication error:', error);
    throw error;
  }
};

module.exports = {
  verifyGoogleToken,
  verifyFacebookToken,
  handleGoogleAuth,
  handleFacebookAuth
};
