// User roles
const USER_ROLES = {
  USER: 'user',
  DRIVER: 'driver',
  ADMIN: 'admin'
};

// Booking statuses
const BOOKING_STATUS = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  DRIVER_ASSIGNED: 'driver_assigned',
  DRIVER_ARRIVED: 'driver_arrived',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  REFUNDED: 'refunded'
};

// Driver statuses
const DRIVER_STATUS = {
  OFFLINE: 'offline',
  ONLINE: 'online',
  BUSY: 'busy',
  INACTIVE: 'inactive'
};

// Driver verification statuses
const DRIVER_VERIFICATION_STATUS = {
  PENDING: 'pending',
  UNDER_REVIEW: 'under_review',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  SUSPENDED: 'suspended'
};

// Vehicle types
const VEHICLE_TYPES = {
  BIKE: 'bike',
  MINI_TRUCK: 'mini_truck',
  TRUCK: 'truck',
  VAN: 'van',
  COURIER: 'courier'
};

// Payment statuses
const PAYMENT_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  REFUNDED: 'refunded',
  PARTIALLY_REFUNDED: 'partially_refunded'
};

// Payment methods
const PAYMENT_METHODS = {
  CASH: 'cash',
  CARD: 'card',
  WALLET: 'wallet',
  UPI: 'upi',
  NET_BANKING: 'net_banking'
};

// Notification types
const NOTIFICATION_TYPES = {
  BOOKING_CONFIRMED: 'booking_confirmed',
  DRIVER_ASSIGNED: 'driver_assigned',
  DRIVER_ARRIVED: 'driver_arrived',
  TRIP_STARTED: 'trip_started',
  TRIP_COMPLETED: 'trip_completed',
  PAYMENT_RECEIVED: 'payment_received',
  BOOKING_CANCELLED: 'booking_cancelled',
  PROMOTIONAL: 'promotional',
  SYSTEM: 'system'
};

// Notification channels
const NOTIFICATION_CHANNELS = {
  SMS: 'sms',
  EMAIL: 'email',
  PUSH: 'push',
  IN_APP: 'in_app'
};

// File upload types
const UPLOAD_TYPES = {
  PROFILE_PICTURE: 'profile_picture',
  DRIVER_LICENSE: 'driver_license',
  VEHICLE_REGISTRATION: 'vehicle_registration',
  INSURANCE: 'insurance',
  IDENTITY_PROOF: 'identity_proof',
  ADDRESS_PROOF: 'address_proof'
};

// API response messages
const RESPONSE_MESSAGES = {
  SUCCESS: 'Operation completed successfully',
  CREATED: 'Resource created successfully',
  UPDATED: 'Resource updated successfully',
  DELETED: 'Resource deleted successfully',
  NOT_FOUND: 'Resource not found',
  UNAUTHORIZED: 'Unauthorized access',
  FORBIDDEN: 'Access forbidden',
  VALIDATION_ERROR: 'Validation error',
  SERVER_ERROR: 'Internal server error',
  DUPLICATE_ENTRY: 'Duplicate entry found'
};

// Rate limiting
const RATE_LIMITS = {
  GENERAL: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // limit each IP to 100 requests per windowMs
  },
  AUTH: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5 // limit each IP to 5 auth requests per windowMs
  },
  OTP: {
    windowMs: 60 * 1000, // 1 minute
    max: 3 // limit each IP to 3 OTP requests per minute
  }
};

// Cache TTL (Time To Live) in seconds
const CACHE_TTL = {
  SHORT: 300, // 5 minutes
  MEDIUM: 1800, // 30 minutes
  LONG: 3600, // 1 hour
  VERY_LONG: 86400 // 24 hours
};

// Pagination defaults
const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 10,
  MAX_LIMIT: 100
};

// Distance and fare calculation
const FARE_CALCULATION = {
  BASE_FARE: 50, // Base fare in currency units
  PER_KM_RATE: 10, // Rate per kilometer
  PER_MINUTE_RATE: 2, // Rate per minute
  SURGE_MULTIPLIER: {
    LOW: 1.0,
    MEDIUM: 1.5,
    HIGH: 2.0,
    PEAK: 2.5
  }
};

module.exports = {
  USER_ROLES,
  BOOKING_STATUS,
  DRIVER_STATUS,
  DRIVER_VERIFICATION_STATUS,
  VEHICLE_TYPES,
  PAYMENT_STATUS,
  PAYMENT_METHODS,
  NOTIFICATION_TYPES,
  NOTIFICATION_CHANNELS,
  UPLOAD_TYPES,
  RESPONSE_MESSAGES,
  RATE_LIMITS,
  CACHE_TTL,
  PAGINATION,
  FARE_CALCULATION
};
