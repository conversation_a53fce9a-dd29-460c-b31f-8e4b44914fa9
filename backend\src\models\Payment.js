const mongoose = require('mongoose');
const { PAYMENT_STATUS, PAYMENT_METHODS } = require('../config/constants');

const paymentSchema = new mongoose.Schema({
  paymentId: {
    type: String,
    unique: true,
    required: true
  },
  booking: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Booking',
    required: true
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  driver: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Driver'
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  currency: {
    type: String,
    default: 'USD',
    uppercase: true
  },
  paymentMethod: {
    type: String,
    enum: Object.values(PAYMENT_METHODS),
    required: true
  },
  status: {
    type: String,
    enum: Object.values(PAYMENT_STATUS),
    default: PAYMENT_STATUS.PENDING
  },
  // Stripe-specific fields
  stripePaymentIntentId: String,
  stripeChargeId: String,
  stripeCustomerId: String,
  stripePaymentMethodId: String,
  
  // Payment breakdown
  breakdown: {
    subtotal: {
      type: Number,
      required: true
    },
    tax: {
      type: Number,
      default: 0
    },
    tips: {
      type: Number,
      default: 0
    },
    discount: {
      type: Number,
      default: 0
    },
    platformFee: {
      type: Number,
      default: 0
    },
    driverEarnings: {
      type: Number,
      required: true
    }
  },
  
  // Transaction details
  transactionId: String,
  gatewayResponse: {
    type: mongoose.Schema.Types.Mixed
  },
  failureReason: String,
  
  // Refund information
  refunds: [{
    refundId: String,
    amount: Number,
    reason: String,
    status: {
      type: String,
      enum: ['pending', 'succeeded', 'failed'],
      default: 'pending'
    },
    processedAt: Date,
    gatewayRefundId: String
  }],
  
  // Payout information (for driver)
  payout: {
    payoutId: String,
    amount: Number,
    status: {
      type: String,
      enum: ['pending', 'in_transit', 'paid', 'failed'],
      default: 'pending'
    },
    scheduledAt: Date,
    processedAt: Date,
    failureReason: String
  },
  
  // Metadata
  metadata: {
    ipAddress: String,
    userAgent: String,
    deviceId: String,
    platform: String
  },
  
  // Timestamps
  authorizedAt: Date,
  capturedAt: Date,
  failedAt: Date,
  refundedAt: Date,
  
  // Webhook events
  webhookEvents: [{
    eventType: String,
    eventId: String,
    processedAt: Date,
    data: mongoose.Schema.Types.Mixed
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
paymentSchema.index({ paymentId: 1 });
paymentSchema.index({ booking: 1 });
paymentSchema.index({ user: 1, createdAt: -1 });
paymentSchema.index({ driver: 1, createdAt: -1 });
paymentSchema.index({ status: 1 });
paymentSchema.index({ paymentMethod: 1 });
paymentSchema.index({ stripePaymentIntentId: 1 });
paymentSchema.index({ createdAt: -1 });
paymentSchema.index({ 'payout.status': 1 });

// Virtual for total refunded amount
paymentSchema.virtual('totalRefunded').get(function() {
  return this.refunds.reduce((total, refund) => {
    return refund.status === 'succeeded' ? total + refund.amount : total;
  }, 0);
});

// Virtual for net amount (after refunds)
paymentSchema.virtual('netAmount').get(function() {
  return this.amount - this.totalRefunded;
});

// Virtual for is fully refunded
paymentSchema.virtual('isFullyRefunded').get(function() {
  return this.totalRefunded >= this.amount;
});

// Pre-save middleware to generate payment ID
paymentSchema.pre('save', function(next) {
  if (!this.paymentId) {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    this.paymentId = `PAY${timestamp}${random}`.toUpperCase();
  }
  next();
});

// Method to add refund
paymentSchema.methods.addRefund = function(amount, reason, gatewayRefundId) {
  this.refunds.push({
    refundId: `REF${Date.now().toString(36)}${Math.random().toString(36).substr(2, 3)}`.toUpperCase(),
    amount,
    reason,
    gatewayRefundId,
    processedAt: new Date()
  });
  
  if (this.totalRefunded >= this.amount) {
    this.status = PAYMENT_STATUS.REFUNDED;
  } else if (this.totalRefunded > 0) {
    this.status = PAYMENT_STATUS.PARTIALLY_REFUNDED;
  }
  
  return this.save();
};

// Method to update status
paymentSchema.methods.updateStatus = function(newStatus, metadata = {}) {
  this.status = newStatus;
  
  const statusTimestamps = {
    [PAYMENT_STATUS.PROCESSING]: 'authorizedAt',
    [PAYMENT_STATUS.COMPLETED]: 'capturedAt',
    [PAYMENT_STATUS.FAILED]: 'failedAt',
    [PAYMENT_STATUS.REFUNDED]: 'refundedAt'
  };
  
  if (statusTimestamps[newStatus]) {
    this[statusTimestamps[newStatus]] = new Date();
  }
  
  if (metadata.failureReason) {
    this.failureReason = metadata.failureReason;
  }
  
  if (metadata.gatewayResponse) {
    this.gatewayResponse = metadata.gatewayResponse;
  }
  
  return this.save();
};

// Method to process payout
paymentSchema.methods.processPayout = function(payoutData) {
  this.payout = {
    payoutId: payoutData.payoutId,
    amount: payoutData.amount,
    status: payoutData.status || 'pending',
    scheduledAt: payoutData.scheduledAt || new Date(),
    processedAt: payoutData.processedAt,
    failureReason: payoutData.failureReason
  };
  
  return this.save();
};

// Static method to get payment statistics
paymentSchema.statics.getStatistics = function(startDate, endDate, filters = {}) {
  const matchStage = {
    createdAt: { $gte: startDate, $lte: endDate },
    ...filters
  };
  
  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalPayments: { $sum: 1 },
        totalAmount: { $sum: '$amount' },
        totalRefunded: { $sum: '$totalRefunded' },
        averageAmount: { $avg: '$amount' },
        successfulPayments: {
          $sum: { $cond: [{ $eq: ['$status', PAYMENT_STATUS.COMPLETED] }, 1, 0] }
        },
        failedPayments: {
          $sum: { $cond: [{ $eq: ['$status', PAYMENT_STATUS.FAILED] }, 1, 0] }
        }
      }
    }
  ]);
};

module.exports = mongoose.model('Payment', paymentSchema);
