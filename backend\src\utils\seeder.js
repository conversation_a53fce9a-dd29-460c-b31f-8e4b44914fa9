const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const { User, Driver, PromoCode } = require('../models');
const { USER_ROLES, VEHICLE_TYPES, DRIVER_VERIFICATION_STATUS } = require('../config/constants');
const logger = require('../config/logger');

// Sample data for seeding
const sampleUsers = [
  {
    firstName: 'Admin',
    lastName: 'User',
    email: '<EMAIL>',
    phone: '+1234567890',
    password: 'admin123',
    role: USER_ROLES.ADMIN,
    isEmailVerified: true,
    isPhoneVerified: true
  },
  {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+1234567891',
    password: 'password123',
    role: USER_ROLES.USER,
    isEmailVerified: true,
    isPhoneVerified: true,
    addresses: [{
      type: 'home',
      label: 'Home',
      address: '123 Main St, New York, NY 10001',
      coordinates: {
        type: 'Point',
        coordinates: [-73.935242, 40.730610]
      },
      city: 'New York',
      state: 'NY',
      country: 'USA',
      postalCode: '10001',
      isDefault: true
    }]
  },
  {
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
    phone: '+1234567892',
    password: 'password123',
    role: USER_ROLES.USER,
    isEmailVerified: true,
    isPhoneVerified: true,
    addresses: [{
      type: 'work',
      label: 'Office',
      address: '456 Business Ave, New York, NY 10002',
      coordinates: {
        type: 'Point',
        coordinates: [-73.925242, 40.740610]
      },
      city: 'New York',
      state: 'NY',
      country: 'USA',
      postalCode: '10002',
      isDefault: true
    }]
  },
  {
    firstName: 'Mike',
    lastName: 'Driver',
    email: '<EMAIL>',
    phone: '+1234567893',
    password: 'password123',
    role: USER_ROLES.DRIVER,
    isEmailVerified: true,
    isPhoneVerified: true
  },
  {
    firstName: 'Sarah',
    lastName: 'Wilson',
    email: '<EMAIL>',
    phone: '+1234567894',
    password: 'password123',
    role: USER_ROLES.DRIVER,
    isEmailVerified: true,
    isPhoneVerified: true
  }
];

const samplePromoCodes = [
  {
    code: 'WELCOME10',
    title: 'Welcome Discount',
    description: 'Get 10% off on your first booking',
    type: 'percentage',
    value: 10,
    maxDiscountAmount: 50,
    minOrderAmount: 20,
    usageLimit: {
      total: 1000,
      perUser: 1
    },
    validFrom: new Date(),
    validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
    applicableFor: {
      userTypes: ['new_user'],
      firstTimeUsers: true
    },
    isActive: true,
    tags: ['welcome', 'new_user']
  },
  {
    code: 'SAVE20',
    title: 'Save $20',
    description: 'Get $20 off on orders above $100',
    type: 'fixed_amount',
    value: 20,
    minOrderAmount: 100,
    usageLimit: {
      total: 500,
      perUser: 3
    },
    validFrom: new Date(),
    validUntil: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
    applicableFor: {
      userTypes: ['existing_user', 'premium_user']
    },
    isActive: true,
    tags: ['discount', 'save']
  },
  {
    code: 'FREEDELIVERY',
    title: 'Free Delivery',
    description: 'Get free delivery on all orders',
    type: 'free_delivery',
    value: 15, // Assuming $15 delivery fee
    minOrderAmount: 50,
    usageLimit: {
      total: 200,
      perUser: 2
    },
    validFrom: new Date(),
    validUntil: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 days from now
    applicableFor: {
      vehicleTypes: [VEHICLE_TYPES.COURIER, VEHICLE_TYPES.BIKE]
    },
    restrictions: {
      daysOfWeek: [1, 2, 3, 4, 5] // Monday to Friday
    },
    isActive: true,
    tags: ['free_delivery', 'weekday']
  }
];

const seedDatabase = async () => {
  try {
    logger.info('Starting database seeding...');

    // Clear existing data
    await User.deleteMany({});
    await Driver.deleteMany({});
    await PromoCode.deleteMany({});
    
    logger.info('Cleared existing data');

    // Create users
    const createdUsers = [];
    for (const userData of sampleUsers) {
      const user = new User(userData);
      await user.save();
      createdUsers.push(user);
      logger.info(`Created user: ${user.email}`);
    }

    // Create drivers for users with driver role
    const driverUsers = createdUsers.filter(user => user.role === USER_ROLES.DRIVER);
    
    for (const driverUser of driverUsers) {
      const driverData = {
        user: driverUser._id,
        driverLicense: {
          number: `DL${Math.random().toString(36).substr(2, 9).toUpperCase()}`,
          expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
          class: 'CDL-B',
          state: 'NY'
        },
        vehicles: [{
          type: Math.random() > 0.5 ? VEHICLE_TYPES.MINI_TRUCK : VEHICLE_TYPES.VAN,
          make: Math.random() > 0.5 ? 'Ford' : 'Chevrolet',
          model: Math.random() > 0.5 ? 'Transit' : 'Express',
          year: 2020 + Math.floor(Math.random() * 4),
          color: ['White', 'Blue', 'Red', 'Black'][Math.floor(Math.random() * 4)],
          licensePlate: `NY${Math.random().toString(36).substr(2, 6).toUpperCase()}`,
          capacity: {
            weight: 1000 + Math.floor(Math.random() * 2000),
            volume: 10 + Math.floor(Math.random() * 20),
            passengers: 2
          },
          features: ['gps', 'air_conditioning'],
          isActive: true
        }],
        verificationStatus: DRIVER_VERIFICATION_STATUS.APPROVED,
        verifiedAt: new Date(),
        currentLocation: {
          type: 'Point',
          coordinates: [-73.935242 + (Math.random() - 0.5) * 0.1, 40.730610 + (Math.random() - 0.5) * 0.1]
        },
        lastLocationUpdate: new Date(),
        workingHours: {
          monday: { start: '09:00', end: '18:00', isWorking: true },
          tuesday: { start: '09:00', end: '18:00', isWorking: true },
          wednesday: { start: '09:00', end: '18:00', isWorking: true },
          thursday: { start: '09:00', end: '18:00', isWorking: true },
          friday: { start: '09:00', end: '18:00', isWorking: true },
          saturday: { start: '10:00', end: '16:00', isWorking: true },
          sunday: { start: '10:00', end: '16:00', isWorking: false }
        },
        bankDetails: {
          accountHolderName: `${driverUser.firstName} ${driverUser.lastName}`,
          accountNumber: Math.random().toString().substr(2, 10),
          bankName: 'Sample Bank',
          isVerified: true
        },
        emergencyContact: {
          name: 'Emergency Contact',
          phone: '+**********',
          relationship: 'Spouse'
        },
        onboardingCompletedAt: new Date(),
        lastActiveAt: new Date()
      };

      const driver = new Driver(driverData);
      await driver.save();
      logger.info(`Created driver profile for: ${driverUser.email}`);
    }

    // Create promo codes
    const adminUser = createdUsers.find(user => user.role === USER_ROLES.ADMIN);
    
    for (const promoData of samplePromoCodes) {
      promoData.createdBy = adminUser._id;
      promoData.approvedBy = adminUser._id;
      promoData.approvedAt = new Date();
      
      const promoCode = new PromoCode(promoData);
      await promoCode.save();
      logger.info(`Created promo code: ${promoCode.code}`);
    }

    logger.info('Database seeding completed successfully!');
    
    // Log summary
    const userCount = await User.countDocuments();
    const driverCount = await Driver.countDocuments();
    const promoCount = await PromoCode.countDocuments();
    
    logger.info(`Seeding Summary:`);
    logger.info(`- Users created: ${userCount}`);
    logger.info(`- Drivers created: ${driverCount}`);
    logger.info(`- Promo codes created: ${promoCount}`);

  } catch (error) {
    logger.error('Database seeding failed:', error);
    throw error;
  }
};

const clearDatabase = async () => {
  try {
    logger.info('Clearing database...');
    
    await User.deleteMany({});
    await Driver.deleteMany({});
    await PromoCode.deleteMany({});
    
    logger.info('Database cleared successfully!');
  } catch (error) {
    logger.error('Database clearing failed:', error);
    throw error;
  }
};

module.exports = {
  seedDatabase,
  clearDatabase
};
