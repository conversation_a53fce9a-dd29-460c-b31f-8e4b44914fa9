const Joi = require('joi');
const { USER_ROLES } = require('../config/constants');

// Common validation schemas
const emailSchema = Joi.string()
  .email()
  .lowercase()
  .trim()
  .required()
  .messages({
    'string.email': 'Please provide a valid email address',
    'any.required': 'Email is required'
  });

const phoneSchema = Joi.string()
  .pattern(/^\+?[1-9]\d{1,14}$/)
  .required()
  .messages({
    'string.pattern.base': 'Please provide a valid phone number',
    'any.required': 'Phone number is required'
  });

const passwordSchema = Joi.string()
  .min(6)
  .max(128)
  .required()
  .messages({
    'string.min': 'Password must be at least 6 characters long',
    'string.max': 'Password cannot exceed 128 characters',
    'any.required': 'Password is required'
  });

const nameSchema = Joi.string()
  .trim()
  .min(2)
  .max(50)
  .pattern(/^[a-zA-Z\s]+$/)
  .required()
  .messages({
    'string.min': 'Name must be at least 2 characters long',
    'string.max': 'Name cannot exceed 50 characters',
    'string.pattern.base': 'Name can only contain letters and spaces',
    'any.required': 'Name is required'
  });

// Registration validation
const registerValidation = Joi.object({
  firstName: nameSchema.messages({
    'any.required': 'First name is required'
  }),
  lastName: nameSchema.messages({
    'any.required': 'Last name is required'
  }),
  email: emailSchema,
  phone: phoneSchema,
  password: passwordSchema,
  role: Joi.string()
    .valid(...Object.values(USER_ROLES))
    .default(USER_ROLES.USER)
    .messages({
      'any.only': 'Invalid role specified'
    })
});

// Login validation
const loginValidation = Joi.object({
  email: emailSchema,
  password: Joi.string()
    .required()
    .messages({
      'any.required': 'Password is required'
    })
});

// Refresh token validation
const refreshTokenValidation = Joi.object({
  refreshToken: Joi.string()
    .required()
    .messages({
      'any.required': 'Refresh token is required'
    })
});

// Phone verification validation
const phoneVerificationValidation = Joi.object({
  otp: Joi.string()
    .length(6)
    .pattern(/^\d{6}$/)
    .required()
    .messages({
      'string.length': 'OTP must be exactly 6 digits',
      'string.pattern.base': 'OTP must contain only numbers',
      'any.required': 'OTP is required'
    })
});

// Email verification validation (for URL parameter)
const emailVerificationValidation = Joi.object({
  token: Joi.string()
    .length(64)
    .hex()
    .required()
    .messages({
      'string.length': 'Invalid verification token',
      'string.hex': 'Invalid verification token format',
      'any.required': 'Verification token is required'
    })
});

// Password reset request validation
const passwordResetRequestValidation = Joi.object({
  email: emailSchema
});

// Password reset validation
const passwordResetValidation = Joi.object({
  token: Joi.string()
    .required()
    .messages({
      'any.required': 'Reset token is required'
    }),
  password: passwordSchema
});

// Change password validation
const changePasswordValidation = Joi.object({
  currentPassword: Joi.string()
    .required()
    .messages({
      'any.required': 'Current password is required'
    }),
  newPassword: passwordSchema.messages({
    'any.required': 'New password is required'
  })
});

// Update profile validation
const updateProfileValidation = Joi.object({
  firstName: nameSchema.optional(),
  lastName: nameSchema.optional(),
  dateOfBirth: Joi.date()
    .max('now')
    .optional()
    .messages({
      'date.max': 'Date of birth cannot be in the future'
    }),
  gender: Joi.string()
    .valid('male', 'female', 'other')
    .optional()
    .messages({
      'any.only': 'Gender must be male, female, or other'
    }),
  preferences: Joi.object({
    notifications: Joi.object({
      email: Joi.boolean().optional(),
      sms: Joi.boolean().optional(),
      push: Joi.boolean().optional()
    }).optional(),
    language: Joi.string()
      .min(2)
      .max(5)
      .optional(),
    currency: Joi.string()
      .length(3)
      .uppercase()
      .optional()
  }).optional()
});

// Address validation
const addressValidation = Joi.object({
  type: Joi.string()
    .valid('home', 'work', 'other')
    .default('home'),
  label: Joi.string()
    .trim()
    .min(1)
    .max(50)
    .required()
    .messages({
      'string.min': 'Address label is required',
      'string.max': 'Address label cannot exceed 50 characters',
      'any.required': 'Address label is required'
    }),
  address: Joi.string()
    .trim()
    .min(10)
    .max(200)
    .required()
    .messages({
      'string.min': 'Address must be at least 10 characters long',
      'string.max': 'Address cannot exceed 200 characters',
      'any.required': 'Address is required'
    }),
  coordinates: Joi.object({
    type: Joi.string()
      .valid('Point')
      .default('Point'),
    coordinates: Joi.array()
      .items(Joi.number())
      .length(2)
      .required()
      .messages({
        'array.length': 'Coordinates must contain exactly 2 numbers [longitude, latitude]',
        'any.required': 'Coordinates are required'
      })
  }).required(),
  city: Joi.string()
    .trim()
    .max(50)
    .optional(),
  state: Joi.string()
    .trim()
    .max(50)
    .optional(),
  country: Joi.string()
    .trim()
    .max(50)
    .optional(),
  postalCode: Joi.string()
    .trim()
    .max(20)
    .optional(),
  isDefault: Joi.boolean()
    .default(false)
});

// Social auth validation
const googleAuthValidation = Joi.object({
  idToken: Joi.string()
    .required()
    .messages({
      'any.required': 'Google ID token is required'
    })
});

const facebookAuthValidation = Joi.object({
  accessToken: Joi.string()
    .required()
    .messages({
      'any.required': 'Facebook access token is required'
    })
});

// Notification preferences validation
const notificationPreferencesValidation = Joi.object({
  email: Joi.boolean().optional(),
  sms: Joi.boolean().optional(),
  push: Joi.boolean().optional()
});

// Deactivate account validation
const deactivateAccountValidation = Joi.object({
  password: Joi.string()
    .required()
    .messages({
      'any.required': 'Password is required to deactivate account'
    })
});

// Validation middleware
const validate = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors
      });
    }

    req.body = value;
    next();
  };
};

// Validation middleware for URL parameters
const validateParams = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.params, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      return res.status(400).json({
        success: false,
        error: 'Parameter validation failed',
        details: errors
      });
    }

    req.params = value;
    next();
  };
};

module.exports = {
  // Validation schemas
  registerValidation,
  loginValidation,
  refreshTokenValidation,
  phoneVerificationValidation,
  emailVerificationValidation,
  passwordResetRequestValidation,
  passwordResetValidation,
  changePasswordValidation,
  updateProfileValidation,
  addressValidation,
  googleAuthValidation,
  facebookAuthValidation,
  notificationPreferencesValidation,
  deactivateAccountValidation,

  // Validation middleware
  validate,
  validateParams
};
