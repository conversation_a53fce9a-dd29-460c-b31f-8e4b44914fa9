#!/usr/bin/env node

require('dotenv').config();
const mongoose = require('mongoose');
const { seedDatabase, clearDatabase } = require('../src/utils/seeder');
const logger = require('../src/config/logger');

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    logger.info('MongoDB connected for seeding');
  } catch (error) {
    logger.error('Database connection failed:', error);
    process.exit(1);
  }
};

const main = async () => {
  const command = process.argv[2];
  
  await connectDB();
  
  try {
    switch (command) {
      case 'seed':
        await seedDatabase();
        break;
      case 'clear':
        await clearDatabase();
        break;
      case 'reset':
        await clearDatabase();
        await seedDatabase();
        break;
      default:
        logger.info('Available commands:');
        logger.info('  seed  - Seed the database with sample data');
        logger.info('  clear - Clear all data from the database');
        logger.info('  reset - Clear and then seed the database');
        logger.info('');
        logger.info('Usage: node scripts/seed.js <command>');
        break;
    }
  } catch (error) {
    logger.error('Seeding operation failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    logger.info('Database connection closed');
    process.exit(0);
  }
};

main();
