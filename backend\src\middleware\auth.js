const { verifyToken, extractTokenFromHeader } = require('../utils/jwt');
const { User } = require('../models');
const { USER_ROLES } = require('../config/constants');
const logger = require('../config/logger');

/**
 * Middleware to authenticate user using JWT token
 */
const authenticate = async (req, res, next) => {
  try {
    const authHeader = req.header('Authorization');
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'Access denied. No token provided.'
      });
    }

    // Verify token
    const decoded = verifyToken(token, process.env.JWT_SECRET);
    
    // Find user
    const user = await User.findById(decoded.userId).select('-password');
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Invalid token. User not found.'
      });
    }

    // Check if user is active
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        error: 'Account is deactivated.'
      });
    }

    // Check if account is locked
    if (user.isLocked) {
      return res.status(401).json({
        success: false,
        error: 'Account is temporarily locked due to multiple failed login attempts.'
      });
    }

    // Add user to request object
    req.user = user;
    req.token = token;
    
    next();
  } catch (error) {
    logger.error('Authentication error:', error);
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        error: 'Invalid token.'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        error: 'Token expired.'
      });
    }
    
    return res.status(500).json({
      success: false,
      error: 'Authentication failed.'
    });
  }
};

/**
 * Middleware to authorize user based on roles
 * @param {...String} roles - Allowed roles
 */
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required.'
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'Access forbidden. Insufficient permissions.'
      });
    }

    next();
  };
};

/**
 * Middleware to check if user is verified (email and phone)
 */
const requireVerification = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required.'
    });
  }

  if (!req.user.isEmailVerified) {
    return res.status(403).json({
      success: false,
      error: 'Email verification required.',
      code: 'EMAIL_NOT_VERIFIED'
    });
  }

  if (!req.user.isPhoneVerified) {
    return res.status(403).json({
      success: false,
      error: 'Phone verification required.',
      code: 'PHONE_NOT_VERIFIED'
    });
  }

  next();
};

/**
 * Middleware to check if user owns the resource or is admin
 * @param {String} userIdField - Field name containing user ID in request params/body
 */
const requireOwnership = (userIdField = 'userId') => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required.'
      });
    }

    const resourceUserId = req.params[userIdField] || req.body[userIdField];
    
    // Admin can access any resource
    if (req.user.role === USER_ROLES.ADMIN) {
      return next();
    }

    // User can only access their own resources
    if (req.user._id.toString() !== resourceUserId) {
      return res.status(403).json({
        success: false,
        error: 'Access forbidden. You can only access your own resources.'
      });
    }

    next();
  };
};

/**
 * Middleware for optional authentication (doesn't fail if no token)
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.header('Authorization');
    const token = extractTokenFromHeader(authHeader);

    if (token) {
      const decoded = verifyToken(token, process.env.JWT_SECRET);
      const user = await User.findById(decoded.userId).select('-password');
      
      if (user && user.isActive && !user.isLocked) {
        req.user = user;
        req.token = token;
      }
    }
    
    next();
  } catch (error) {
    // Continue without authentication if token is invalid
    next();
  }
};

/**
 * Middleware to check if user is admin or the resource owner
 */
const adminOrOwner = (userIdField = 'userId') => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required.'
      });
    }

    const resourceUserId = req.params[userIdField] || req.body[userIdField];
    
    // Admin can access any resource
    if (req.user.role === USER_ROLES.ADMIN) {
      return next();
    }

    // User can access their own resources
    if (req.user._id.toString() === resourceUserId) {
      return next();
    }

    return res.status(403).json({
      success: false,
      error: 'Access forbidden. Admin privileges or resource ownership required.'
    });
  };
};

/**
 * Middleware to check if user has driver profile
 */
const requireDriverProfile = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required.'
      });
    }

    if (req.user.role !== USER_ROLES.DRIVER) {
      return res.status(403).json({
        success: false,
        error: 'Driver role required.'
      });
    }

    // Check if driver profile exists
    const { Driver } = require('../models');
    const driver = await Driver.findOne({ user: req.user._id });
    
    if (!driver) {
      return res.status(403).json({
        success: false,
        error: 'Driver profile not found. Please complete driver registration.',
        code: 'DRIVER_PROFILE_NOT_FOUND'
      });
    }

    req.driver = driver;
    next();
  } catch (error) {
    logger.error('Driver profile check error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to verify driver profile.'
    });
  }
};

/**
 * Middleware to check if driver is verified
 */
const requireVerifiedDriver = async (req, res, next) => {
  try {
    // First check if driver profile exists
    await requireDriverProfile(req, res, () => {});
    
    if (!req.driver) {
      return; // Error already handled by requireDriverProfile
    }

    if (req.driver.verificationStatus !== 'approved') {
      return res.status(403).json({
        success: false,
        error: 'Driver verification required. Please wait for admin approval.',
        code: 'DRIVER_NOT_VERIFIED',
        verificationStatus: req.driver.verificationStatus
      });
    }

    next();
  } catch (error) {
    logger.error('Driver verification check error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to verify driver status.'
    });
  }
};

/**
 * Rate limiting middleware for authentication endpoints
 */
const authRateLimit = require('express-rate-limit')({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: {
    success: false,
    error: 'Too many authentication attempts, please try again later.',
    retryAfter: 15 * 60 // 15 minutes in seconds
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * Rate limiting middleware for OTP requests
 */
const otpRateLimit = require('express-rate-limit')({
  windowMs: 60 * 1000, // 1 minute
  max: 3, // limit each IP to 3 OTP requests per minute
  message: {
    success: false,
    error: 'Too many OTP requests, please try again later.',
    retryAfter: 60 // 1 minute in seconds
  },
  standardHeaders: true,
  legacyHeaders: false,
});

module.exports = {
  authenticate,
  authorize,
  requireVerification,
  requireOwnership,
  optionalAuth,
  adminOrOwner,
  requireDriverProfile,
  requireVerifiedDriver,
  authRateLimit,
  otpRateLimit
};
