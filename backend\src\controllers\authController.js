const { User } = require('../models');
const {
  generateTokenPair,
  refreshAccessToken,
  revokeRefreshToken,
  revokeAllRefreshTokens,
  generateVerificationToken,
  generateOTP,
  hashToken
} = require('../utils/jwt');
const { handleGoogleAuth, handleFacebookAuth } = require('../utils/socialAuth');
const { RESPONSE_MESSAGES, USER_ROLES } = require('../config/constants');
const logger = require('../config/logger');

/**
 * Register a new user
 */
const register = async (req, res) => {
  try {
    const { firstName, lastName, email, phone, password, role = USER_ROLES.USER } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ email }, { phone }]
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: existingUser.email === email 
          ? 'User with this email already exists' 
          : 'User with this phone number already exists'
      });
    }

    // Create new user
    const user = new User({
      firstName,
      lastName,
      email,
      phone,
      password,
      role
    });

    // Generate email verification token
    const emailVerificationToken = generateVerificationToken();
    user.emailVerificationToken = hashToken(emailVerificationToken);
    user.emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    // Generate phone verification OTP
    const phoneOTP = generateOTP();
    user.phoneVerificationToken = hashToken(phoneOTP);
    user.phoneVerificationExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    await user.save();

    // Generate tokens
    const deviceInfo = req.get('User-Agent') || 'unknown';
    const tokens = await generateTokenPair(user, deviceInfo);

    // TODO: Send verification email and SMS
    // await sendVerificationEmail(user.email, emailVerificationToken);
    // await sendVerificationSMS(user.phone, phoneOTP);

    logger.info(`New user registered: ${user.email}`);

    res.status(201).json({
      success: true,
      message: 'User registered successfully. Please verify your email and phone.',
      data: {
        user: {
          id: user._id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phone: user.phone,
          role: user.role,
          isEmailVerified: user.isEmailVerified,
          isPhoneVerified: user.isPhoneVerified
        },
        tokens
      }
    });
  } catch (error) {
    logger.error('Registration error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Login user
 */
const login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Find user and include password for comparison
    const user = await User.findOne({ email }).select('+password');
    
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Invalid email or password'
      });
    }

    // Check if account is locked
    if (user.isLocked) {
      return res.status(423).json({
        success: false,
        error: 'Account is temporarily locked due to multiple failed login attempts',
        lockUntil: user.lockUntil
      });
    }

    // Check if account is active
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        error: 'Account is deactivated'
      });
    }

    // Compare password
    const isPasswordValid = await user.comparePassword(password);
    
    if (!isPasswordValid) {
      // Increment login attempts
      await user.incLoginAttempts();
      
      return res.status(401).json({
        success: false,
        error: 'Invalid email or password'
      });
    }

    // Reset login attempts on successful login
    if (user.loginAttempts > 0) {
      await user.resetLoginAttempts();
    }

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    // Generate tokens
    const deviceInfo = req.get('User-Agent') || 'unknown';
    const tokens = await generateTokenPair(user, deviceInfo);

    logger.info(`User logged in: ${user.email}`);

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: user._id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phone: user.phone,
          role: user.role,
          isEmailVerified: user.isEmailVerified,
          isPhoneVerified: user.isPhoneVerified,
          lastLogin: user.lastLogin
        },
        tokens
      }
    });
  } catch (error) {
    logger.error('Login error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Refresh access token
 */
const refresh = async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({
        success: false,
        error: 'Refresh token is required'
      });
    }

    const tokens = await refreshAccessToken(refreshToken);

    res.json({
      success: true,
      message: 'Token refreshed successfully',
      data: { tokens }
    });
  } catch (error) {
    logger.error('Token refresh error:', error);
    
    if (error.message.includes('expired') || error.message.includes('invalid')) {
      return res.status(401).json({
        success: false,
        error: error.message
      });
    }
    
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Logout user
 */
const logout = async (req, res) => {
  try {
    const { refreshToken } = req.body;
    const userId = req.user._id;

    if (refreshToken) {
      await revokeRefreshToken(userId, refreshToken);
    }

    logger.info(`User logged out: ${req.user.email}`);

    res.json({
      success: true,
      message: 'Logout successful'
    });
  } catch (error) {
    logger.error('Logout error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Logout from all devices
 */
const logoutAll = async (req, res) => {
  try {
    const userId = req.user._id;
    await revokeAllRefreshTokens(userId);

    logger.info(`User logged out from all devices: ${req.user.email}`);

    res.json({
      success: true,
      message: 'Logged out from all devices successfully'
    });
  } catch (error) {
    logger.error('Logout all error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Get current user profile
 */
const getProfile = async (req, res) => {
  try {
    const user = await User.findById(req.user._id).populate('addresses');

    res.json({
      success: true,
      data: {
        user: {
          id: user._id,
          firstName: user.firstName,
          lastName: user.lastName,
          fullName: user.fullName,
          email: user.email,
          phone: user.phone,
          role: user.role,
          profilePicture: user.profilePicture,
          dateOfBirth: user.dateOfBirth,
          gender: user.gender,
          addresses: user.addresses,
          isEmailVerified: user.isEmailVerified,
          isPhoneVerified: user.isPhoneVerified,
          preferences: user.preferences,
          lastLogin: user.lastLogin,
          createdAt: user.createdAt
        }
      }
    });
  } catch (error) {
    logger.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Verify email
 */
const verifyEmail = async (req, res) => {
  try {
    const { token } = req.params;
    const hashedToken = hashToken(token);

    const user = await User.findOne({
      emailVerificationToken: hashedToken,
      emailVerificationExpires: { $gt: Date.now() }
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        error: 'Invalid or expired verification token'
      });
    }

    user.isEmailVerified = true;
    user.emailVerificationToken = undefined;
    user.emailVerificationExpires = undefined;
    await user.save();

    logger.info(`Email verified for user: ${user.email}`);

    res.json({
      success: true,
      message: 'Email verified successfully'
    });
  } catch (error) {
    logger.error('Email verification error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Verify phone with OTP
 */
const verifyPhone = async (req, res) => {
  try {
    const { otp } = req.body;
    const userId = req.user._id;
    const hashedOTP = hashToken(otp);

    const user = await User.findOne({
      _id: userId,
      phoneVerificationToken: hashedOTP,
      phoneVerificationExpires: { $gt: Date.now() }
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        error: 'Invalid or expired OTP'
      });
    }

    user.isPhoneVerified = true;
    user.phoneVerificationToken = undefined;
    user.phoneVerificationExpires = undefined;
    await user.save();

    logger.info(`Phone verified for user: ${user.email}`);

    res.json({
      success: true,
      message: 'Phone verified successfully'
    });
  } catch (error) {
    logger.error('Phone verification error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Resend email verification
 */
const resendEmailVerification = async (req, res) => {
  try {
    const user = await User.findById(req.user._id);

    if (user.isEmailVerified) {
      return res.status(400).json({
        success: false,
        error: 'Email is already verified'
      });
    }

    // Generate new verification token
    const emailVerificationToken = generateVerificationToken();
    user.emailVerificationToken = hashToken(emailVerificationToken);
    user.emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
    await user.save();

    // TODO: Send verification email
    // await sendVerificationEmail(user.email, emailVerificationToken);

    res.json({
      success: true,
      message: 'Verification email sent successfully'
    });
  } catch (error) {
    logger.error('Resend email verification error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Resend phone verification OTP
 */
const resendPhoneVerification = async (req, res) => {
  try {
    const user = await User.findById(req.user._id);

    if (user.isPhoneVerified) {
      return res.status(400).json({
        success: false,
        error: 'Phone is already verified'
      });
    }

    // Generate new OTP
    const phoneOTP = generateOTP();
    user.phoneVerificationToken = hashToken(phoneOTP);
    user.phoneVerificationExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
    await user.save();

    // TODO: Send SMS with OTP
    // await sendVerificationSMS(user.phone, phoneOTP);

    res.json({
      success: true,
      message: 'Verification OTP sent successfully'
    });
  } catch (error) {
    logger.error('Resend phone verification error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Google OAuth authentication
 */
const googleAuth = async (req, res) => {
  try {
    const { idToken } = req.body;

    if (!idToken) {
      return res.status(400).json({
        success: false,
        error: 'Google ID token is required'
      });
    }

    const deviceInfo = req.get('User-Agent') || 'unknown';
    const result = await handleGoogleAuth(idToken, deviceInfo);

    res.json({
      success: true,
      message: result.isNewUser ? 'Account created successfully' : 'Login successful',
      data: result
    });
  } catch (error) {
    logger.error('Google authentication error:', error);

    if (error.message.includes('Invalid')) {
      return res.status(400).json({
        success: false,
        error: error.message
      });
    }

    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Facebook OAuth authentication
 */
const facebookAuth = async (req, res) => {
  try {
    const { accessToken } = req.body;

    if (!accessToken) {
      return res.status(400).json({
        success: false,
        error: 'Facebook access token is required'
      });
    }

    const deviceInfo = req.get('User-Agent') || 'unknown';
    const result = await handleFacebookAuth(accessToken, deviceInfo);

    res.json({
      success: true,
      message: result.isNewUser ? 'Account created successfully' : 'Login successful',
      data: result
    });
  } catch (error) {
    logger.error('Facebook authentication error:', error);

    if (error.message.includes('Invalid')) {
      return res.status(400).json({
        success: false,
        error: error.message
      });
    }

    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

module.exports = {
  register,
  login,
  refresh,
  logout,
  logoutAll,
  getProfile,
  verifyEmail,
  verifyPhone,
  resendEmailVerification,
  resendPhoneVerification,
  googleAuth,
  facebookAuth
};
