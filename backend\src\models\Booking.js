const mongoose = require('mongoose');
const { BOOKING_STATUS, VEHICLE_TYPES, PAYMENT_STATUS } = require('../config/constants');

const locationSchema = new mongoose.Schema({
  address: {
    type: String,
    required: true
  },
  coordinates: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point'
    },
    coordinates: {
      type: [Number], // [longitude, latitude]
      required: true
    }
  },
  city: String,
  state: String,
  country: String,
  postalCode: String,
  contactName: String,
  contactPhone: String,
  instructions: String
});

const fareBreakdownSchema = new mongoose.Schema({
  baseFare: {
    type: Number,
    required: true,
    min: 0
  },
  distanceFare: {
    type: Number,
    default: 0,
    min: 0
  },
  timeFare: {
    type: Number,
    default: 0,
    min: 0
  },
  surgeMultiplier: {
    type: Number,
    default: 1,
    min: 1
  },
  discount: {
    type: Number,
    default: 0,
    min: 0
  },
  tax: {
    type: Number,
    default: 0,
    min: 0
  },
  tips: {
    type: Number,
    default: 0,
    min: 0
  },
  totalFare: {
    type: Number,
    required: true,
    min: 0
  }
});

const trackingSchema = new mongoose.Schema({
  status: {
    type: String,
    required: true
  },
  timestamp: {
    type: Date,
    default: Date.now
  },
  location: {
    type: {
      type: String,
      enum: ['Point']
    },
    coordinates: [Number]
  },
  notes: String,
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
});

const bookingSchema = new mongoose.Schema({
  bookingId: {
    type: String,
    unique: true,
    required: true
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  driver: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Driver'
  },
  vehicleType: {
    type: String,
    enum: Object.values(VEHICLE_TYPES),
    required: true
  },
  serviceType: {
    type: String,
    enum: ['on_demand', 'scheduled'],
    default: 'on_demand'
  },
  scheduledDateTime: Date,
  pickupLocation: {
    type: locationSchema,
    required: true
  },
  dropoffLocations: [{
    type: locationSchema,
    required: true
  }],
  packageDetails: {
    description: String,
    weight: Number, // in kg
    dimensions: {
      length: Number,
      width: Number,
      height: Number
    },
    value: Number,
    isFragile: {
      type: Boolean,
      default: false
    },
    requiresSignature: {
      type: Boolean,
      default: false
    },
    specialInstructions: String
  },
  distance: {
    type: Number, // in km
    required: true
  },
  estimatedDuration: {
    type: Number, // in minutes
    required: true
  },
  actualDuration: Number, // in minutes
  fareBreakdown: fareBreakdownSchema,
  status: {
    type: String,
    enum: Object.values(BOOKING_STATUS),
    default: BOOKING_STATUS.PENDING
  },
  paymentStatus: {
    type: String,
    enum: Object.values(PAYMENT_STATUS),
    default: PAYMENT_STATUS.PENDING
  },
  paymentMethod: {
    type: String,
    enum: ['cash', 'card', 'wallet', 'upi'],
    required: true
  },
  paymentIntentId: String, // Stripe payment intent ID
  promoCode: {
    code: String,
    discountAmount: Number,
    discountPercentage: Number
  },
  tracking: [trackingSchema],
  driverAssignedAt: Date,
  driverArrivedAt: Date,
  tripStartedAt: Date,
  tripCompletedAt: Date,
  cancelledAt: Date,
  cancellationReason: String,
  cancelledBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  rating: {
    userRating: {
      score: {
        type: Number,
        min: 1,
        max: 5
      },
      comment: String,
      ratedAt: Date
    },
    driverRating: {
      score: {
        type: Number,
        min: 1,
        max: 5
      },
      comment: String,
      ratedAt: Date
    }
  },
  route: {
    polyline: String, // Encoded polyline from Google Maps
    waypoints: [{
      location: {
        type: {
          type: String,
          enum: ['Point']
        },
        coordinates: [Number]
      },
      timestamp: Date,
      speed: Number
    }]
  },
  estimatedArrival: Date,
  actualArrival: Date,
  photos: [{
    type: String, // URL to photo
    description: String,
    takenAt: Date,
    takenBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  signature: {
    recipientName: String,
    signatureUrl: String,
    signedAt: Date
  },
  invoice: {
    invoiceNumber: String,
    generatedAt: Date,
    pdfUrl: String
  },
  refund: {
    amount: Number,
    reason: String,
    processedAt: Date,
    refundId: String
  },
  metadata: {
    platform: {
      type: String,
      enum: ['web', 'mobile_ios', 'mobile_android'],
      default: 'web'
    },
    appVersion: String,
    deviceId: String,
    ipAddress: String,
    userAgent: String
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
bookingSchema.index({ bookingId: 1 });
bookingSchema.index({ user: 1, createdAt: -1 });
bookingSchema.index({ driver: 1, createdAt: -1 });
bookingSchema.index({ status: 1 });
bookingSchema.index({ paymentStatus: 1 });
bookingSchema.index({ vehicleType: 1 });
bookingSchema.index({ serviceType: 1 });
bookingSchema.index({ scheduledDateTime: 1 });
bookingSchema.index({ 'pickupLocation.coordinates': '2dsphere' });
bookingSchema.index({ 'dropoffLocations.coordinates': '2dsphere' });
bookingSchema.index({ createdAt: -1 });
bookingSchema.index({ driverAssignedAt: -1 });
bookingSchema.index({ tripCompletedAt: -1 });

// Virtual for total stops
bookingSchema.virtual('totalStops').get(function() {
  return this.dropoffLocations.length;
});

// Virtual for is multi-stop
bookingSchema.virtual('isMultiStop').get(function() {
  return this.dropoffLocations.length > 1;
});

// Virtual for trip duration
bookingSchema.virtual('tripDuration').get(function() {
  if (this.tripStartedAt && this.tripCompletedAt) {
    return Math.round((this.tripCompletedAt - this.tripStartedAt) / (1000 * 60)); // in minutes
  }
  return null;
});

// Virtual for waiting time
bookingSchema.virtual('waitingTime').get(function() {
  if (this.driverArrivedAt && this.tripStartedAt) {
    return Math.round((this.tripStartedAt - this.driverArrivedAt) / (1000 * 60)); // in minutes
  }
  return null;
});

// Pre-save middleware to generate booking ID
bookingSchema.pre('save', function(next) {
  if (!this.bookingId) {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    this.bookingId = `BK${timestamp}${random}`.toUpperCase();
  }
  next();
});

// Method to add tracking update
bookingSchema.methods.addTracking = function(status, location, notes, updatedBy) {
  this.tracking.push({
    status,
    location,
    notes,
    updatedBy,
    timestamp: new Date()
  });
  this.status = status;
  return this.save();
};

// Method to calculate fare
bookingSchema.methods.calculateFare = function(baseFare, perKmRate, perMinuteRate, surgeMultiplier = 1) {
  const distanceFare = this.distance * perKmRate;
  const timeFare = this.estimatedDuration * perMinuteRate;
  const subtotal = (baseFare + distanceFare + timeFare) * surgeMultiplier;
  const discount = this.promoCode ? (this.promoCode.discountAmount || (subtotal * this.promoCode.discountPercentage / 100)) : 0;
  const tax = (subtotal - discount) * 0.1; // 10% tax
  const totalFare = subtotal - discount + tax;

  this.fareBreakdown = {
    baseFare,
    distanceFare,
    timeFare,
    surgeMultiplier,
    discount,
    tax,
    tips: 0,
    totalFare
  };

  return this.save();
};

// Method to update status
bookingSchema.methods.updateStatus = function(newStatus, updatedBy, notes) {
  const statusTimestamps = {
    [BOOKING_STATUS.DRIVER_ASSIGNED]: 'driverAssignedAt',
    [BOOKING_STATUS.DRIVER_ARRIVED]: 'driverArrivedAt',
    [BOOKING_STATUS.IN_PROGRESS]: 'tripStartedAt',
    [BOOKING_STATUS.COMPLETED]: 'tripCompletedAt',
    [BOOKING_STATUS.CANCELLED]: 'cancelledAt'
  };

  this.status = newStatus;
  if (statusTimestamps[newStatus]) {
    this[statusTimestamps[newStatus]] = new Date();
  }

  return this.addTracking(newStatus, null, notes, updatedBy);
};

module.exports = mongoose.model('Booking', bookingSchema);
