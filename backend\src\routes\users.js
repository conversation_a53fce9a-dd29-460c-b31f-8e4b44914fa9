const express = require('express');
const multer = require('multer');
const path = require('path');
const router = express.Router();

// Import controllers
const {
  updateProfile,
  uploadProfilePicture,
  getAddresses,
  addAddress,
  updateAddress,
  deleteAddress,
  setDefaultAddress,
  changePassword,
  updateNotificationPreferences,
  deactivateAccount
} = require('../controllers/userController');

// Import middleware
const {
  authenticate,
  requireVerification
} = require('../middleware/auth');

// Import validators
const {
  validate,
  updateProfileValidation,
  addressValidation,
  changePasswordValidation,
  notificationPreferencesValidation,
  deactivateAccountValidation
} = require('../validators/authValidator');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/profiles/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, `profile-${req.user._id}-${uniqueSuffix}${path.extname(file.originalname)}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

/**
 * @route   PUT /api/v1/users/profile
 * @desc    Update user profile
 * @access  Private
 */
router.put('/profile',
  authenticate,
  validate(updateProfileValidation),
  updateProfile
);

/**
 * @route   POST /api/v1/users/profile-picture
 * @desc    Upload profile picture
 * @access  Private
 */
router.post('/profile-picture',
  authenticate,
  upload.single('profilePicture'),
  uploadProfilePicture
);

/**
 * @route   GET /api/v1/users/addresses
 * @desc    Get user addresses
 * @access  Private
 */
router.get('/addresses',
  authenticate,
  getAddresses
);

/**
 * @route   POST /api/v1/users/addresses
 * @desc    Add new address
 * @access  Private
 */
router.post('/addresses',
  authenticate,
  requireVerification,
  validate(addressValidation),
  addAddress
);

/**
 * @route   PUT /api/v1/users/addresses/:addressId
 * @desc    Update address
 * @access  Private
 */
router.put('/addresses/:addressId',
  authenticate,
  requireVerification,
  validate(addressValidation),
  updateAddress
);

/**
 * @route   DELETE /api/v1/users/addresses/:addressId
 * @desc    Delete address
 * @access  Private
 */
router.delete('/addresses/:addressId',
  authenticate,
  deleteAddress
);

/**
 * @route   PUT /api/v1/users/addresses/:addressId/default
 * @desc    Set default address
 * @access  Private
 */
router.put('/addresses/:addressId/default',
  authenticate,
  setDefaultAddress
);

/**
 * @route   PUT /api/v1/users/change-password
 * @desc    Change password
 * @access  Private
 */
router.put('/change-password',
  authenticate,
  validate(changePasswordValidation),
  changePassword
);

/**
 * @route   PUT /api/v1/users/notification-preferences
 * @desc    Update notification preferences
 * @access  Private
 */
router.put('/notification-preferences',
  authenticate,
  validate(notificationPreferencesValidation),
  updateNotificationPreferences
);

/**
 * @route   POST /api/v1/users/deactivate
 * @desc    Deactivate account
 * @access  Private
 */
router.post('/deactivate',
  authenticate,
  validate(deactivateAccountValidation),
  deactivateAccount
);

module.exports = router;
