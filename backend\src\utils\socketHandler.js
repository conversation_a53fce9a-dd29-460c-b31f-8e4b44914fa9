const logger = require('../config/logger');

const socketHandler = (io) => {
  io.on('connection', (socket) => {
    logger.info(`Socket connected: ${socket.id}`);

    // Handle user joining a room (for real-time updates)
    socket.on('join_room', (data) => {
      const { userId, userType } = data;
      const room = `${userType}_${userId}`;
      socket.join(room);
      logger.info(`User ${userId} joined room: ${room}`);
    });

    // Handle driver location updates
    socket.on('driver_location_update', (data) => {
      const { driverId, location, bookingId } = data;
      
      // Broadcast location to user if booking exists
      if (bookingId) {
        socket.to(`booking_${bookingId}`).emit('driver_location', {
          driverId,
          location,
          timestamp: new Date()
        });
      }
    });

    // Handle booking updates
    socket.on('booking_update', (data) => {
      const { bookingId, status, message } = data;
      
      // Broadcast to all users in the booking room
      io.to(`booking_${bookingId}`).emit('booking_status_update', {
        bookingId,
        status,
        message,
        timestamp: new Date()
      });
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      logger.info(`Socket disconnected: ${socket.id}`);
    });

    // Handle errors
    socket.on('error', (error) => {
      logger.error(`Socket error for ${socket.id}:`, error);
    });
  });

  return io;
};

module.exports = socketHandler;
